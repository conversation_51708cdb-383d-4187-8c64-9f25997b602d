package enums

const (
	AdminRefGitID int64 = 1

	REPO_TYPE string = "repo_type"
	NAMESPACE string = "namespace"
	REPO_NAME string = "repo_name"
	USER_ID   string = "user_id"
	ORG_ID    string = "org_id"
	ECR_ID    string = "ecr_id"
)

type OrderByColumn string // @name OrderByColumn

const (
	OrderByColumn_ExpiresAt OrderByColumn = "expires_at"
	OrderByColumn_CreatedAt OrderByColumn = "created_at"
	OrderByColumn_UpdatedAt OrderByColumn = "updated_at"
)

type OrderByDirection string // @name OrderByDirection

const (
	OrderByDirection_Asc  OrderByDirection = "asc"
	OrderByDirection_Desc OrderByDirection = "desc"
)

type ArgoWorkflowStatus string

const (
	ArgoWorkflowStatus_Pending    ArgoWorkflowStatus = "Pending"
	ArgoWorkflowStatus_Running    ArgoWorkflowStatus = "Running"
	ArgoWorkflowStatus_Error      ArgoWorkflowStatus = "Error"
	ArgoWorkflowStatus_Succeeded  ArgoWorkflowStatus = "Succeeded"
	ArgoWorkflowStatus_Failed     ArgoWorkflowStatus = "Failed"
	ArgoWorkflowStatus_NotRunning ArgoWorkflowStatus = "Not Running"
	ArgoWorkflowStatus_Terminated ArgoWorkflowStatus = "Terminated"
)

type MemoryUnit string

const (
	MemoryUnit_MiB MemoryUnit = "MiB"
	MemoryUnit_GiB MemoryUnit = "GiB"
)

func (m MemoryUnit) ToK8SMemUnit() K8SMemoryUnit {
	var unit K8SMemoryUnit
	switch m {
	case MemoryUnit_MiB:
		unit = K8SMemoryUnit_Mi
	case MemoryUnit_GiB:
		unit = K8SMemoryUnit_Gi
	}
	return unit
}

type K8SMemoryUnit string

const (
	K8SMemoryUnit_Mi K8SMemoryUnit = "Mi"
	K8SMemoryUnit_Gi K8SMemoryUnit = "Gi"
)

const CPU = "cpu"

type DeploymentType string

const (
	DeploymentType_Space DeploymentType = "space"
	DeploymentType_ECR   DeploymentType = "ecr"
)

const NvidiaGPUResourceName = "nvidia.com/gpu"
