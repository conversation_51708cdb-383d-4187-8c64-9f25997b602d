// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	dto "api-server/internal/dto"
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockOrganizationUsecase is an autogenerated mock type for the OrganizationUsecase type
type MockOrganizationUsecase struct {
	mock.Mock
}

type MockOrganizationUsecase_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrganizationUsecase) EXPECT() *MockOrganizationUsecase_Expecter {
	return &MockOrganizationUsecase_Expecter{mock: &_m.Mock}
}

// AuthorizeAccessToken provides a mock function with given fields: ctx, accessToken
func (_m *MockOrganizationUsecase) AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error) {
	ret := _m.Called(ctx, accessToken)

	if len(ret) == 0 {
		panic("no return value specified for AuthorizeAccessToken")
	}

	var r0 *uuid.UUID
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*uuid.UUID, error)); ok {
		return rf(ctx, accessToken)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *uuid.UUID); ok {
		r0 = rf(ctx, accessToken)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*uuid.UUID)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, accessToken)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_AuthorizeAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AuthorizeAccessToken'
type MockOrganizationUsecase_AuthorizeAccessToken_Call struct {
	*mock.Call
}

// AuthorizeAccessToken is a helper method to define mock.On call
//   - ctx context.Context
//   - accessToken string
func (_e *MockOrganizationUsecase_Expecter) AuthorizeAccessToken(ctx interface{}, accessToken interface{}) *MockOrganizationUsecase_AuthorizeAccessToken_Call {
	return &MockOrganizationUsecase_AuthorizeAccessToken_Call{Call: _e.mock.On("AuthorizeAccessToken", ctx, accessToken)}
}

func (_c *MockOrganizationUsecase_AuthorizeAccessToken_Call) Run(run func(ctx context.Context, accessToken string)) *MockOrganizationUsecase_AuthorizeAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(string))
	})
	return _c
}

func (_c *MockOrganizationUsecase_AuthorizeAccessToken_Call) Return(_a0 *uuid.UUID, _a1 error) *MockOrganizationUsecase_AuthorizeAccessToken_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_AuthorizeAccessToken_Call) RunAndReturn(run func(context.Context, string) (*uuid.UUID, error)) *MockOrganizationUsecase_AuthorizeAccessToken_Call {
	_c.Call.Return(run)
	return _c
}

// CheckRepositoryInOrg provides a mock function with given fields: ctx, orgId, repoID
func (_m *MockOrganizationUsecase) CheckRepositoryInOrg(ctx context.Context, orgId uuid.UUID, repoID types.RepoID) (bool, error) {
	ret := _m.Called(ctx, orgId, repoID)

	if len(ret) == 0 {
		panic("no return value specified for CheckRepositoryInOrg")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID) (bool, error)); ok {
		return rf(ctx, orgId, repoID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, types.RepoID) bool); ok {
		r0 = rf(ctx, orgId, repoID)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID, types.RepoID) error); ok {
		r1 = rf(ctx, orgId, repoID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_CheckRepositoryInOrg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CheckRepositoryInOrg'
type MockOrganizationUsecase_CheckRepositoryInOrg_Call struct {
	*mock.Call
}

// CheckRepositoryInOrg is a helper method to define mock.On call
//   - ctx context.Context
//   - orgId uuid.UUID
//   - repoID types.RepoID
func (_e *MockOrganizationUsecase_Expecter) CheckRepositoryInOrg(ctx interface{}, orgId interface{}, repoID interface{}) *MockOrganizationUsecase_CheckRepositoryInOrg_Call {
	return &MockOrganizationUsecase_CheckRepositoryInOrg_Call{Call: _e.mock.On("CheckRepositoryInOrg", ctx, orgId, repoID)}
}

func (_c *MockOrganizationUsecase_CheckRepositoryInOrg_Call) Run(run func(ctx context.Context, orgId uuid.UUID, repoID types.RepoID)) *MockOrganizationUsecase_CheckRepositoryInOrg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(types.RepoID))
	})
	return _c
}

func (_c *MockOrganizationUsecase_CheckRepositoryInOrg_Call) Return(_a0 bool, _a1 error) *MockOrganizationUsecase_CheckRepositoryInOrg_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_CheckRepositoryInOrg_Call) RunAndReturn(run func(context.Context, uuid.UUID, types.RepoID) (bool, error)) *MockOrganizationUsecase_CheckRepositoryInOrg_Call {
	_c.Call.Return(run)
	return _c
}

// CreateOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) CreateOrganization(ctx context.Context, input dto.CreateOrganizationInput) (*dto.CreateOrganizationOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrganization")
	}

	var r0 *dto.CreateOrganizationOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateOrganizationInput) (*dto.CreateOrganizationOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.CreateOrganizationInput) *dto.CreateOrganizationOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.CreateOrganizationOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.CreateOrganizationInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_CreateOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrganization'
type MockOrganizationUsecase_CreateOrganization_Call struct {
	*mock.Call
}

// CreateOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.CreateOrganizationInput
func (_e *MockOrganizationUsecase_Expecter) CreateOrganization(ctx interface{}, input interface{}) *MockOrganizationUsecase_CreateOrganization_Call {
	return &MockOrganizationUsecase_CreateOrganization_Call{Call: _e.mock.On("CreateOrganization", ctx, input)}
}

func (_c *MockOrganizationUsecase_CreateOrganization_Call) Run(run func(ctx context.Context, input dto.CreateOrganizationInput)) *MockOrganizationUsecase_CreateOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.CreateOrganizationInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_CreateOrganization_Call) Return(_a0 *dto.CreateOrganizationOutput, _a1 error) *MockOrganizationUsecase_CreateOrganization_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_CreateOrganization_Call) RunAndReturn(run func(context.Context, dto.CreateOrganizationInput) (*dto.CreateOrganizationOutput, error)) *MockOrganizationUsecase_CreateOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAvatarOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) DeleteAvatarOrganization(ctx context.Context, input dto.DeleteOrganizationAvatarInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAvatarOrganization")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteOrganizationAvatarInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_DeleteAvatarOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAvatarOrganization'
type MockOrganizationUsecase_DeleteAvatarOrganization_Call struct {
	*mock.Call
}

// DeleteAvatarOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteOrganizationAvatarInput
func (_e *MockOrganizationUsecase_Expecter) DeleteAvatarOrganization(ctx interface{}, input interface{}) *MockOrganizationUsecase_DeleteAvatarOrganization_Call {
	return &MockOrganizationUsecase_DeleteAvatarOrganization_Call{Call: _e.mock.On("DeleteAvatarOrganization", ctx, input)}
}

func (_c *MockOrganizationUsecase_DeleteAvatarOrganization_Call) Run(run func(ctx context.Context, input dto.DeleteOrganizationAvatarInput)) *MockOrganizationUsecase_DeleteAvatarOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteOrganizationAvatarInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_DeleteAvatarOrganization_Call) Return(_a0 error) *MockOrganizationUsecase_DeleteAvatarOrganization_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_DeleteAvatarOrganization_Call) RunAndReturn(run func(context.Context, dto.DeleteOrganizationAvatarInput) error) *MockOrganizationUsecase_DeleteAvatarOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) DeleteOrganization(ctx context.Context, input dto.DeleteOrganizationInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for DeleteOrganization")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.DeleteOrganizationInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_DeleteOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrganization'
type MockOrganizationUsecase_DeleteOrganization_Call struct {
	*mock.Call
}

// DeleteOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.DeleteOrganizationInput
func (_e *MockOrganizationUsecase_Expecter) DeleteOrganization(ctx interface{}, input interface{}) *MockOrganizationUsecase_DeleteOrganization_Call {
	return &MockOrganizationUsecase_DeleteOrganization_Call{Call: _e.mock.On("DeleteOrganization", ctx, input)}
}

func (_c *MockOrganizationUsecase_DeleteOrganization_Call) Run(run func(ctx context.Context, input dto.DeleteOrganizationInput)) *MockOrganizationUsecase_DeleteOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.DeleteOrganizationInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_DeleteOrganization_Call) Return(_a0 error) *MockOrganizationUsecase_DeleteOrganization_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_DeleteOrganization_Call) RunAndReturn(run func(context.Context, dto.DeleteOrganizationInput) error) *MockOrganizationUsecase_DeleteOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrganizationByID provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) FindOrganizationByID(ctx context.Context, input dto.FindOrganizationInput) (*dto.FindOrganizationOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for FindOrganizationByID")
	}

	var r0 *dto.FindOrganizationOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.FindOrganizationInput) (*dto.FindOrganizationOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.FindOrganizationInput) *dto.FindOrganizationOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.FindOrganizationOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.FindOrganizationInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_FindOrganizationByID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrganizationByID'
type MockOrganizationUsecase_FindOrganizationByID_Call struct {
	*mock.Call
}

// FindOrganizationByID is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.FindOrganizationInput
func (_e *MockOrganizationUsecase_Expecter) FindOrganizationByID(ctx interface{}, input interface{}) *MockOrganizationUsecase_FindOrganizationByID_Call {
	return &MockOrganizationUsecase_FindOrganizationByID_Call{Call: _e.mock.On("FindOrganizationByID", ctx, input)}
}

func (_c *MockOrganizationUsecase_FindOrganizationByID_Call) Run(run func(ctx context.Context, input dto.FindOrganizationInput)) *MockOrganizationUsecase_FindOrganizationByID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.FindOrganizationInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_FindOrganizationByID_Call) Return(_a0 *dto.FindOrganizationOutput, _a1 error) *MockOrganizationUsecase_FindOrganizationByID_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_FindOrganizationByID_Call) RunAndReturn(run func(context.Context, dto.FindOrganizationInput) (*dto.FindOrganizationOutput, error)) *MockOrganizationUsecase_FindOrganizationByID_Call {
	_c.Call.Return(run)
	return _c
}

// GetMember provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) GetMember(ctx context.Context, input dto.GetMemberOrganizaionInput) (*entities.OrgMember, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for GetMember")
	}

	var r0 *entities.OrgMember
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetMemberOrganizaionInput) (*entities.OrgMember, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.GetMemberOrganizaionInput) *entities.OrgMember); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.OrgMember)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.GetMemberOrganizaionInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_GetMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMember'
type MockOrganizationUsecase_GetMember_Call struct {
	*mock.Call
}

// GetMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.GetMemberOrganizaionInput
func (_e *MockOrganizationUsecase_Expecter) GetMember(ctx interface{}, input interface{}) *MockOrganizationUsecase_GetMember_Call {
	return &MockOrganizationUsecase_GetMember_Call{Call: _e.mock.On("GetMember", ctx, input)}
}

func (_c *MockOrganizationUsecase_GetMember_Call) Run(run func(ctx context.Context, input dto.GetMemberOrganizaionInput)) *MockOrganizationUsecase_GetMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.GetMemberOrganizaionInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_GetMember_Call) Return(_a0 *entities.OrgMember, _a1 error) *MockOrganizationUsecase_GetMember_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_GetMember_Call) RunAndReturn(run func(context.Context, dto.GetMemberOrganizaionInput) (*entities.OrgMember, error)) *MockOrganizationUsecase_GetMember_Call {
	_c.Call.Return(run)
	return _c
}

// InviteMultipleOrgMembers provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) InviteMultipleOrgMembers(ctx context.Context, input dto.InviteOrgMembersInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteMultipleOrgMembers")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.InviteOrgMembersInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_InviteMultipleOrgMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteMultipleOrgMembers'
type MockOrganizationUsecase_InviteMultipleOrgMembers_Call struct {
	*mock.Call
}

// InviteMultipleOrgMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.InviteOrgMembersInput
func (_e *MockOrganizationUsecase_Expecter) InviteMultipleOrgMembers(ctx interface{}, input interface{}) *MockOrganizationUsecase_InviteMultipleOrgMembers_Call {
	return &MockOrganizationUsecase_InviteMultipleOrgMembers_Call{Call: _e.mock.On("InviteMultipleOrgMembers", ctx, input)}
}

func (_c *MockOrganizationUsecase_InviteMultipleOrgMembers_Call) Run(run func(ctx context.Context, input dto.InviteOrgMembersInput)) *MockOrganizationUsecase_InviteMultipleOrgMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.InviteOrgMembersInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_InviteMultipleOrgMembers_Call) Return(_a0 error) *MockOrganizationUsecase_InviteMultipleOrgMembers_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_InviteMultipleOrgMembers_Call) RunAndReturn(run func(context.Context, dto.InviteOrgMembersInput) error) *MockOrganizationUsecase_InviteMultipleOrgMembers_Call {
	_c.Call.Return(run)
	return _c
}

// InviteUser provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) InviteUser(ctx context.Context, input dto.InviteOrgMemberInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for InviteUser")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.InviteOrgMemberInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockOrganizationUsecase_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.InviteOrgMemberInput
func (_e *MockOrganizationUsecase_Expecter) InviteUser(ctx interface{}, input interface{}) *MockOrganizationUsecase_InviteUser_Call {
	return &MockOrganizationUsecase_InviteUser_Call{Call: _e.mock.On("InviteUser", ctx, input)}
}

func (_c *MockOrganizationUsecase_InviteUser_Call) Run(run func(ctx context.Context, input dto.InviteOrgMemberInput)) *MockOrganizationUsecase_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.InviteOrgMemberInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_InviteUser_Call) Return(_a0 error) *MockOrganizationUsecase_InviteUser_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_InviteUser_Call) RunAndReturn(run func(context.Context, dto.InviteOrgMemberInput) error) *MockOrganizationUsecase_InviteUser_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrganizationMembers provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) ListOrganizationMembers(ctx context.Context, input dto.ListOrgMembersInput) (*dto.ListOrgMembersOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListOrganizationMembers")
	}

	var r0 *dto.ListOrgMembersOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) (*dto.ListOrgMembersOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrgMembersInput) *dto.ListOrgMembersOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListOrgMembersOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrgMembersInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_ListOrganizationMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrganizationMembers'
type MockOrganizationUsecase_ListOrganizationMembers_Call struct {
	*mock.Call
}

// ListOrganizationMembers is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrgMembersInput
func (_e *MockOrganizationUsecase_Expecter) ListOrganizationMembers(ctx interface{}, input interface{}) *MockOrganizationUsecase_ListOrganizationMembers_Call {
	return &MockOrganizationUsecase_ListOrganizationMembers_Call{Call: _e.mock.On("ListOrganizationMembers", ctx, input)}
}

func (_c *MockOrganizationUsecase_ListOrganizationMembers_Call) Run(run func(ctx context.Context, input dto.ListOrgMembersInput)) *MockOrganizationUsecase_ListOrganizationMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrgMembersInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_ListOrganizationMembers_Call) Return(_a0 *dto.ListOrgMembersOutput, _a1 error) *MockOrganizationUsecase_ListOrganizationMembers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_ListOrganizationMembers_Call) RunAndReturn(run func(context.Context, dto.ListOrgMembersInput) (*dto.ListOrgMembersOutput, error)) *MockOrganizationUsecase_ListOrganizationMembers_Call {
	_c.Call.Return(run)
	return _c
}

// ListOrganizations provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) ListOrganizations(ctx context.Context, input dto.ListOrganizationsInput) (*dto.ListOrganizationsOutput, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for ListOrganizations")
	}

	var r0 *dto.ListOrganizationsOutput
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) (*dto.ListOrganizationsOutput, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.ListOrganizationsInput) *dto.ListOrganizationsOutput); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.ListOrganizationsOutput)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.ListOrganizationsInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_ListOrganizations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrganizations'
type MockOrganizationUsecase_ListOrganizations_Call struct {
	*mock.Call
}

// ListOrganizations is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.ListOrganizationsInput
func (_e *MockOrganizationUsecase_Expecter) ListOrganizations(ctx interface{}, input interface{}) *MockOrganizationUsecase_ListOrganizations_Call {
	return &MockOrganizationUsecase_ListOrganizations_Call{Call: _e.mock.On("ListOrganizations", ctx, input)}
}

func (_c *MockOrganizationUsecase_ListOrganizations_Call) Run(run func(ctx context.Context, input dto.ListOrganizationsInput)) *MockOrganizationUsecase_ListOrganizations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.ListOrganizationsInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_ListOrganizations_Call) Return(_a0 *dto.ListOrganizationsOutput, _a1 error) *MockOrganizationUsecase_ListOrganizations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_ListOrganizations_Call) RunAndReturn(run func(context.Context, dto.ListOrganizationsInput) (*dto.ListOrganizationsOutput, error)) *MockOrganizationUsecase_ListOrganizations_Call {
	_c.Call.Return(run)
	return _c
}

// RemoveMember provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) RemoveMember(ctx context.Context, input dto.RemoveMemberOrganizaionInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for RemoveMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.RemoveMemberOrganizaionInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_RemoveMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveMember'
type MockOrganizationUsecase_RemoveMember_Call struct {
	*mock.Call
}

// RemoveMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.RemoveMemberOrganizaionInput
func (_e *MockOrganizationUsecase_Expecter) RemoveMember(ctx interface{}, input interface{}) *MockOrganizationUsecase_RemoveMember_Call {
	return &MockOrganizationUsecase_RemoveMember_Call{Call: _e.mock.On("RemoveMember", ctx, input)}
}

func (_c *MockOrganizationUsecase_RemoveMember_Call) Run(run func(ctx context.Context, input dto.RemoveMemberOrganizaionInput)) *MockOrganizationUsecase_RemoveMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.RemoveMemberOrganizaionInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_RemoveMember_Call) Return(_a0 error) *MockOrganizationUsecase_RemoveMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_RemoveMember_Call) RunAndReturn(run func(context.Context, dto.RemoveMemberOrganizaionInput) error) *MockOrganizationUsecase_RemoveMember_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateMember provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) UpdateMember(ctx context.Context, input dto.UpdateMemberOrganizaionInput) error {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateMember")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateMemberOrganizaionInput) error); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockOrganizationUsecase_UpdateMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMember'
type MockOrganizationUsecase_UpdateMember_Call struct {
	*mock.Call
}

// UpdateMember is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateMemberOrganizaionInput
func (_e *MockOrganizationUsecase_Expecter) UpdateMember(ctx interface{}, input interface{}) *MockOrganizationUsecase_UpdateMember_Call {
	return &MockOrganizationUsecase_UpdateMember_Call{Call: _e.mock.On("UpdateMember", ctx, input)}
}

func (_c *MockOrganizationUsecase_UpdateMember_Call) Run(run func(ctx context.Context, input dto.UpdateMemberOrganizaionInput)) *MockOrganizationUsecase_UpdateMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateMemberOrganizaionInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_UpdateMember_Call) Return(_a0 error) *MockOrganizationUsecase_UpdateMember_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockOrganizationUsecase_UpdateMember_Call) RunAndReturn(run func(context.Context, dto.UpdateMemberOrganizaionInput) error) *MockOrganizationUsecase_UpdateMember_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) UpdateOrganization(ctx context.Context, input dto.UpdateOrganizationInput) (*dto.Organization, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateOrganization")
	}

	var r0 *dto.Organization
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateOrganizationInput) (*dto.Organization, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.UpdateOrganizationInput) *dto.Organization); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.Organization)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.UpdateOrganizationInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_UpdateOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOrganization'
type MockOrganizationUsecase_UpdateOrganization_Call struct {
	*mock.Call
}

// UpdateOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UpdateOrganizationInput
func (_e *MockOrganizationUsecase_Expecter) UpdateOrganization(ctx interface{}, input interface{}) *MockOrganizationUsecase_UpdateOrganization_Call {
	return &MockOrganizationUsecase_UpdateOrganization_Call{Call: _e.mock.On("UpdateOrganization", ctx, input)}
}

func (_c *MockOrganizationUsecase_UpdateOrganization_Call) Run(run func(ctx context.Context, input dto.UpdateOrganizationInput)) *MockOrganizationUsecase_UpdateOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UpdateOrganizationInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_UpdateOrganization_Call) Return(_a0 *dto.Organization, _a1 error) *MockOrganizationUsecase_UpdateOrganization_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_UpdateOrganization_Call) RunAndReturn(run func(context.Context, dto.UpdateOrganizationInput) (*dto.Organization, error)) *MockOrganizationUsecase_UpdateOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// UploadAvatarOrganization provides a mock function with given fields: ctx, input
func (_m *MockOrganizationUsecase) UploadAvatarOrganization(ctx context.Context, input dto.UploadOrganizationAvatarInput) (string, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UploadAvatarOrganization")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadOrganizationAvatarInput) (string, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, dto.UploadOrganizationAvatarInput) string); ok {
		r0 = rf(ctx, input)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, dto.UploadOrganizationAvatarInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockOrganizationUsecase_UploadAvatarOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadAvatarOrganization'
type MockOrganizationUsecase_UploadAvatarOrganization_Call struct {
	*mock.Call
}

// UploadAvatarOrganization is a helper method to define mock.On call
//   - ctx context.Context
//   - input dto.UploadOrganizationAvatarInput
func (_e *MockOrganizationUsecase_Expecter) UploadAvatarOrganization(ctx interface{}, input interface{}) *MockOrganizationUsecase_UploadAvatarOrganization_Call {
	return &MockOrganizationUsecase_UploadAvatarOrganization_Call{Call: _e.mock.On("UploadAvatarOrganization", ctx, input)}
}

func (_c *MockOrganizationUsecase_UploadAvatarOrganization_Call) Run(run func(ctx context.Context, input dto.UploadOrganizationAvatarInput)) *MockOrganizationUsecase_UploadAvatarOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(dto.UploadOrganizationAvatarInput))
	})
	return _c
}

func (_c *MockOrganizationUsecase_UploadAvatarOrganization_Call) Return(_a0 string, _a1 error) *MockOrganizationUsecase_UploadAvatarOrganization_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockOrganizationUsecase_UploadAvatarOrganization_Call) RunAndReturn(run func(context.Context, dto.UploadOrganizationAvatarInput) (string, error)) *MockOrganizationUsecase_UploadAvatarOrganization_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockOrganizationUsecase creates a new instance of MockOrganizationUsecase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrganizationUsecase(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrganizationUsecase {
	mock := &MockOrganizationUsecase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
