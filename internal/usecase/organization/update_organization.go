package organization

import (
	"context"
	"errors"

	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/usecase"
	"api-server/pkg/oteltrace"
)

// UpdateOrganization implements the OrganizationUsecase interface for updating organization information.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing organization update details including ID and new values
//
// Returns:
//   - error: Any error that occurred during update
func (u *impl) UpdateOrganization(ctx context.Context, input dto.UpdateOrganizationInput) (*dto.Organization, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.organization.UpdateOrganization")
	defer span.End()

	// Find the organization
	organization, err := u.repository.FindOrganizationByID(ctx, input.OrgId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, usecase.ErrOrganizationNotFound.Error())
			span.RecordError(usecase.ErrOrganizationNotFound)
			return nil, usecase.ErrOrganizationNotFound
		}
		span.SetStatus(codes.Error, "failed to find organization")
		span.RecordError(err)
		return nil, err
	}

	if input.Interest != nil {
		organization.Interest = input.Interest
	}

	if err := u.repository.Save(ctx, organization); err != nil {
		span.SetStatus(codes.Error, "failed to save organization")
		span.RecordError(err)
		return nil, err
	}

	var org dto.Organization
	org = org.FromEntity(*organization)

	span.AddEvent("organization updated successfully")
	span.SetStatus(codes.Ok, "organization updated successfully")
	return &org, nil
}
