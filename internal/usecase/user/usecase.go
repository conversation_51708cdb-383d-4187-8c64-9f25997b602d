package user

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/aws"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/supabase"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/kompose"
	"api-server/internal/usecase/workflow"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

const NANOID_KEY_LENGTH = 36

// UserUsecase defines the interface for managing user-related operations.
// It provides methods for user authorization, management, and profile operations.
type UserUsecase interface {
	// AuthorizePlatform checks if a user has the required platform-level permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to check
	//   - requiredPermissions: The list of permissions required
	//
	// Returns:
	//   - bool: Whether the user has the required permissions
	//   - error: Any error that occurred during authorization
	AuthorizePlatform(ctx context.Context, userID uuid.UUID, requiredPermissions []enums.AppPermission) (bool, error)

	// AuthorizeOrg checks if a user has the required organization-level permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to check
	//   - orgID: The ID of the organization
	//   - requiredPermissions: The list of permissions required
	//
	// Returns:
	//   - bool: Whether the user has the required permissions
	//   - error: Any error that occurred during authorization
	AuthorizeOrg(ctx context.Context, userID uuid.UUID, orgID uuid.UUID, requiredPermissions []enums.RepoPermission) (bool, error)

	// AuthorizeRepo checks if a user has the required repository-level permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to check
	//   - repoID: The ID of the repository
	//   - requiredPermissions: The list of permissions required
	//
	// Returns:
	//   - bool: Whether the user has the required permissions
	//   - error: Any error that occurred during authorization
	AuthorizeRepo(ctx context.Context, userID uuid.UUID, repoID types.RepoID, requiredPermissions []enums.RepoPermission) (bool, error)

	// AuthorizeECR checks if a user has the required ECR-level permissions.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to check
	//   - ecrID: The ID of the ECR
	//   - requiredPermissions: The list of permissions required
	//
	// Returns:
	//   - bool: Whether the user has the required permissions
	//   - error: Any error that occurred during authorization
	AuthorizeECR(ctx context.Context, userID uuid.UUID, ecrID uuid.UUID, requiredPermissions []enums.ECRPermission) (bool, error)

	// AuthorizeAccessToken verifies if an access token is valid and returns the associated user ID.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - accessToken: The access token to verify
	//
	// Returns:
	//   - *uuid.UUID: The ID of the user associated with the token, if valid
	//   - error: Any error that occurred during verification
	AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error)

	// ListUsers retrieves a list of users based on the provided input criteria.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input criteria for listing users
	//
	// Returns:
	//   - dto.ListUsersOutput: The list of users and pagination information
	//   - error: Any error that occurred during retrieval
	ListUsers(ctx context.Context, input dto.ListUsersInput) (dto.ListUsersOutput, error)

	// UpdateRoleUser updates the role of a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for updating the user's role
	//
	// Returns:
	//   - error: Any error that occurred during the update
	UpdateRoleUser(ctx context.Context, input dto.UpdateRoleInput) error

	// GetCurrentUser retrieves information about the current user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userId: The ID of the user to retrieve
	//
	// Returns:
	//   - dto.User: The user information
	//   - error: Any error that occurred during retrieval
	GetCurrentUser(ctx context.Context, userId uuid.UUID) (dto.User, error)

	// DeleteUser deletes a user from the system.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - adminID: The ID of the admin performing the deletion
	//   - userID: The ID of the user to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteUser(ctx context.Context, adminID uuid.UUID, userID uuid.UUID) error

	// UploadAvatarUser uploads an avatar for a user.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for uploading the avatar
	//
	// Returns:
	//   - string: The URL of the uploaded avatar
	//   - error: Any error that occurred during upload
	UploadAvatarUser(ctx context.Context, input dto.UploadUserAvatarInput) (string, error)

	// DeleteAvatarUser deletes a user's avatar.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userId: The ID of the user whose avatar to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteAvatarUser(ctx context.Context, userId uuid.UUID) error

	// IsAdmin checks if a user has admin privileges.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to check
	//
	// Returns:
	//   - bool: Whether the user is an admin
	//   - error: Any error that occurred during the check
	IsAdmin(ctx context.Context, userID uuid.UUID) (bool, error)

	// UpsertUser creates or updates a user in the system.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user to upsert
	//
	// Returns:
	//   - error: Any error that occurred during the operation
	UpsertUser(ctx context.Context, userID uuid.UUID) error

	// ChangePassword changes a user's password.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user whose password to change
	//   - userAccessToken: The user's access token
	//   - input: The input data for changing the password
	//
	// Returns:
	//   - error: Any error that occurred during the password change
	ChangePassword(ctx context.Context, userID uuid.UUID, userAccessToken string, input dto.ChangePasswordInput) error
}

type impl struct {
	repository      repository.Repository
	gitlabClient    gitlab.GitlabClient
	supabaseClient  supabase.SupabaseClient
	aws             aws.AWSClient
	kubeClient      kubernetes.Interface
	workflowUsecase workflow.WorkflowUsecase
	komposeUsecase  kompose.KomposeUsecase
}

var _ UserUsecase = (*impl)(nil)

// New creates a new instance of the user usecase implementation.
// It initializes the usecase with the required dependencies including repository,
// GitLab client, Supabase client, AWS client, Kubernetes client, and workflow usecase.
//
// Parameters:
//   - repository: Repository interface for data access.
//   - gitlab: Client for GitLab API interactions.
//   - supabaseClient: Client for Supabase interactions (e.g., authentication).
//   - aws: Client for AWS services (e.g., S3 for avatars).
//   - kubeClient: Client for Kubernetes API interactions.
//   - workflowUsecase: Usecase for managing workflows.
//
// Returns:
//   - *impl: New instance of the user usecase.
func New(
	repository repository.Repository,
	gitlab gitlab.GitlabClient,
	supabaseClient supabase.SupabaseClient,
	aws aws.AWSClient,
	kubeClient kubernetes.Interface,
	workflowUsecase workflow.WorkflowUsecase,
	komposeUsecase kompose.KomposeUsecase,
) *impl {
	return &impl{
		repository,
		gitlab,
		supabaseClient,
		aws,
		kubeClient,
		workflowUsecase,
		komposeUsecase,
	}
}

func (i *impl) AuthorizePlatform(ctx context.Context, userID uuid.UUID, requiredPermissions []enums.AppPermission) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizePlatform")
	defer span.End()

	records, err := i.repository.GetAppPermission(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("app permissions not found, returning unauthorized")
			span.SetStatus(codes.Ok, "app permissions not found, returning unauthorized")
			return false, nil
		}
		span.SetStatus(codes.Error, "failed to get app permission")
		span.RecordError(err)
		return false, usecase.ErrInternal
	}

	userPermissions := make([]enums.AppPermission, len(records))
	for i, element := range records {
		userPermissions[i] = element.Permission
	}

	isAuthorized := hasPermission(userPermissions, requiredPermissions)
	span.AddEvent("platform authorization checked")
	span.SetStatus(codes.Ok, "platform authorization checked")
	return isAuthorized, nil
}

func (i *impl) AuthorizeOrg(ctx context.Context, userID uuid.UUID, orgID uuid.UUID, requiredPermissions []enums.RepoPermission) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizeOrg")
	defer span.End()

	records, err := i.repository.GetOrgPermission(ctx, userID, orgID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("org permissions not found, returning unauthorized")
			span.SetStatus(codes.Ok, "org permissions not found, returning unauthorized")
			return false, nil
		}
		span.SetStatus(codes.Error, "failed to get org permission")
		span.RecordError(err)
		return false, usecase.ErrInternal
	}

	userPermissions := make([]enums.RepoPermission, len(records))
	for i, element := range records {
		userPermissions[i] = element.Permission
	}

	isAuthorized := hasPermission(userPermissions, requiredPermissions)
	span.AddEvent("organization authorization checked")
	span.SetStatus(codes.Ok, "organization authorization checked")
	return isAuthorized, nil
}

func (i *impl) AuthorizeRepo(ctx context.Context, userID uuid.UUID, repoID types.RepoID, requiredPermissions []enums.RepoPermission) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizeRepo")
	defer span.End()

	repo, err := i.repository.FindRepository(ctx, repository.RepositoryFilter{
		Type:      repoID.RepoType(),
		Namespace: repoID.Namespace(),
		Name:      repoID.RepoName(),
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("repository not found, returning unauthorized")
			span.SetStatus(codes.Ok, "repository not found, returning unauthorized")
			return false, usecase.ErrRepositoryNotFound
		}
		span.SetStatus(codes.Error, "failed to find repository")
		span.RecordError(err)
		return false, err
	}

	records, err := i.repository.GetRepoPermission(ctx, userID, repo.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("repo permissions not found, returning unauthorized")
			span.SetStatus(codes.Ok, "repo permissions not found, returning unauthorized")
			return false, nil
		}
		span.SetStatus(codes.Error, "failed to get repo permission")
		span.RecordError(err)
		return false, usecase.ErrInternal
	}

	userPermissions := make([]enums.RepoPermission, len(records))
	for i, element := range records {
		userPermissions[i] = element.Permission
	}

	isAuthorized := hasPermission(userPermissions, requiredPermissions)
	span.AddEvent("repository authorization checked")
	span.SetStatus(codes.Ok, "repository authorization checked")
	return isAuthorized, nil
}

func (i *impl) AuthorizeECR(ctx context.Context, userID uuid.UUID, ecrID uuid.UUID, requiredPermissions []enums.ECRPermission) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizeECR")
	defer span.End()

	deployment, err := i.repository.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &ecrID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("ecr deployment not found, returning unauthorized")
			span.SetStatus(codes.Ok, "ecr deployment not found, returning unauthorized")
			return false, nil
		}
		span.SetStatus(codes.Error, "failed to get ecr deployment")
		span.RecordError(err)
		return false, usecase.ErrInternal
	}

	isAuthorized := deployment.UserID == nil || *deployment.UserID == userID
	if !isAuthorized {
		span.AddEvent("user is not authorized to access ecr")
		span.SetStatus(codes.Ok, "user is not authorized to access ecr")
		return false, nil
	}

	span.AddEvent("ecr authorization checked")
	span.SetStatus(codes.Ok, "ecr authorization checked")
	return true, nil
}

func (i *impl) AuthorizeAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizeAccessToken")
	defer span.End()

	token, err := i.repository.FindAccessToken(ctx, repository.FindAccessTokenQuery{
		AccessToken: accessToken,
		Revoked:     false,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.AddEvent("access token not found")
			span.SetStatus(codes.Ok, "access token not found")
			return nil, nil
		}
		span.SetStatus(codes.Error, "failed to find access token")
		span.RecordError(err)
		return nil, usecase.ErrInternal
	}

	userID := &token.UserID
	span.AddEvent("access token authorization checked")
	span.SetStatus(codes.Ok, "access token authorization checked")
	return userID, nil
}

// func (i *impl) AuthorizeOrgAccessToken(ctx context.Context, accessToken string) (*uuid.UUID, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.AuthorizeOrgAccessToken")
// 	defer span.End()
//
// 	token, err := i.repository.FindOrgAccessToken(ctx, repository.FindOrgAccessTokenQuery{
// 		OrgAccessToken: accessToken,
// 		Revoked:        false,
// 	})
// 	if err != nil {
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			span.AddEvent("org access token not found, returning unauthorized")
// 			span.SetStatus(codes.Ok, "org access token not found, returning unauthorized")
// 			return nil, nil
// 		}
// 		span.SetStatus(codes.Error, "failed to find org access token")
// 		span.RecordError(err)
// 		return nil, usecase.ErrInternal
// 	}
//
// 	orgId := &token.OrgID
// 	span.AddEvent("org access token authorization checked")
// 	span.SetStatus(codes.Ok, "org access token authorization checked")
// 	return orgId, nil
// }

// hasPermission checks if a user's permissions contain all required permissions.
// It is a generic function that can be used with any comparable permission type.
//
// Parameters:
//   - userPermissions: A slice of permissions that the user possesses.
//   - requiredPermissions: A slice of permissions that are required.
//
// Returns:
//   - bool: True if the user has all required permissions, false otherwise.
func hasPermission[T comparable](userPermissions []T, requiredPermissions []T) bool {
	for _, requiredPermission := range requiredPermissions {
		found := slices.Contains(userPermissions, requiredPermission)
		if !found {
			return false
		}
	}
	return true
}

func (i *impl) GetCurrentUser(ctx context.Context, userId uuid.UUID) (dto.User, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.GetCurrentUser")
	defer span.End()

	user, err := i.repository.FindUserByID(ctx, userId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return dto.User{}, usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return dto.User{}, usecase.ErrInternal
	}

	var userDto dto.User
	userDto = userDto.FromEntity(*user)
	if userDto.Avatar != nil {
		repoImage, err := i.aws.GenPreSignUrl(ctx, *userDto.Avatar)
		if err != nil {
			span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
			span.RecordError(err)
			return dto.User{}, err
		}

		userDto.Avatar = &repoImage
	}

	span.AddEvent("current user retrieved successfully")
	span.SetStatus(codes.Ok, "current user retrieved successfully")
	return userDto, nil
}

func (i *impl) IsAdmin(ctx context.Context, userID uuid.UUID) (bool, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "user.usecase.IsAdmin")
	defer span.End()

	user, err := i.repository.FindUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return false, usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return false, usecase.ErrInternal
	}

	isAmin := user.Role == enums.UserRole_Admin
	span.AddEvent("admin check")
	span.SetStatus(codes.Ok, "admin check")
	return isAmin, nil
}

// UpsertUser ensures a user record exists in the public.users table.
// This function is typically called to handle cases where the user signup or invitation process
// might have been interrupted, leaving the user authenticated in Supabase (auth.users)
// but without a corresponding record in the application's public.users table.
// If the user already exists in public.users, the function does nothing.
// Otherwise, it retrieves the user's details from Supabase (auth.users) and creates
// the corresponding record in public.users, including setting up their GitLab user and groups.
//
// Parameters:
//   - ctx: The context for the operation.
//   - userID: The UUID of the user (typically from Supabase auth.users) to check and potentially create.
//
// Returns:
//   - error: An error if any step of the process fails (e.g., fetching auth user, creating GitLab user/group, creating public user).
//
// this function checks and creates user in public.users if user sign up and invite user flow failed halfway to create user
func (i *impl) UpsertUser(ctx context.Context, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.UpsertUser")
	defer span.End()

	// check whether user is in public.users table
	currentUser, err := i.repository.FindUserByID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	// if user exist then return
	if currentUser != nil {
		return nil
	}
	// INFO: if user does not exist that means user sign up or invite user flow has failed to create a record in public.users table
	// the rest of this function should do that

	// get user exist in Supabase
	// INFO: user should exist in auth.users table because user can only log in after email confirmation
	authUser, err := i.repository.FindAuthUser(ctx, dto.AuthUserFilter{
		Id: &userID,
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrUserNotFound
		}
		return err
	}

	username, err := gitlab.GenerateUsername(authUser.Email)
	if err != nil {
		return err
	}
	// get root user
	adminRefGitID := enums.AdminRefGitID
	adminUser, err := i.repository.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		return err
	}

	var gitlabAdminAccessToken string
	if adminUser.GitlabAccessToken != nil {
		gitlabAdminAccessToken = *adminUser.GitlabAccessToken
	}
	//get corresponding user in gitlab
	users, err := i.gitlabClient.GetUser(ctx, gitlab.GetUserRequest{
		Email:            authUser.Email,
		AdminAccessToken: gitlabAdminAccessToken,
	})
	if err != nil {
		return err
	}

	var gitlabUser gitlab.CreateUserResponse
	if len(users) > 0 {
		gitlabUser = users[0]
	}
	//get corresponding user's groups in gitlab
	gitGroups := map[string]int64{
		fmt.Sprintf("%s/%s", enums.RepoType_Spaces, username):   0,
		fmt.Sprintf("%s/%s", enums.RepoType_Datasets, username): 0,
		fmt.Sprintf("%s/%s", enums.RepoType_Models, username):   0,
		fmt.Sprintf("%s/%s", enums.RepoType_Composes, username): 0,
	}

	for key := range gitGroups {
		orgs, err := i.gitlabClient.GetGroup(ctx, gitlab.GetGroupRequest{
			Token: gitlabAdminAccessToken,
			Path:  key,
		})
		if err != nil {
			return err
		}

		// orgs should exist here
		// because groups should be created at user sign up or invite user flow
		if len(orgs) > 0 {
			gitGroups[key] = int64(orgs[0].Id)
		}
	}

	userGitlabAccessToken, err := i.gitlabClient.CreatePersonalAccessToken(ctx, gitlab.CreatePersonalAccessTokenRequest{
		UserId:     gitlabUser.Id,
		Name:       "volvo-user-auth",
		AdminToken: gitlabAdminAccessToken,
		Scopes:     []gitlab.TokenScope{gitlab.ScopeAPI},
	})
	if err != nil {
		return err
	}

	return i.repository.Transaction(ctx, func(ctx context.Context) error {
		// create new user in public.users table
		expiredAt, err := utils.ConvertStrToTime(userGitlabAccessToken.ExpiredAt, time.DateOnly)
		if err != nil {
			return err
		}
		newUser := entities.User{
			BaseModel: entities.BaseModel{
				ID: userID,
			},
			Name:                       username,
			Username:                   username,
			Role:                       enums.UserRole_User,
			RefGitUserID:               gitlabUser.Id,
			GitlabAccessToken:          &userGitlabAccessToken.Token,
			GitlabAccessTokenExpiresAt: &expiredAt,
		}
		if err := i.repository.Create(ctx, &newUser); err != nil {
			return err
		}
		// create user Git group
		_, err = i.repository.CreateUserGitGroup(ctx, repository.CreateUserGitGroupInput{
			UserID:           newUser.ID,
			RefGitSpacesID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Spaces, username)],
			RefGitDatasetsID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Datasets, username)],
			RefGitModelsID:   gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Models, username)],
			// RefGitComposesID: gitGroups[fmt.Sprintf("%s/%s", enums.RepoType_Composes, username)],
		})
		if err != nil {
			return err
		}

		return nil
	})
}
