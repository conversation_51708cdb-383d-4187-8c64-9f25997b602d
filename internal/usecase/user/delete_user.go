package user

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/gateways/supabase"
	repository "api-server/internal/repositories"
	"api-server/internal/usecase"
	repo_usecase "api-server/internal/usecase/repository"
	"api-server/pkg/oteltrace"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// DeleteUser implements the UserUsecase interface for deleting a user.
// It handles the complete deletion of a user's data across all systems including GitLab, Supabase,
// and local database, including their repositories, organizations, and access tokens.
//
// Parameters:
//   - ctx: Context for the operation
//   - adminID: UUID of the admin performing the deletion
//   - userID: UUID of the user to be deleted
//
// Returns:
//   - error: Any error that occurred during user deletion
func (i *impl) DeleteUser(ctx context.Context, adminID uuid.UUID, userID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteUser")
	defer span.End()

	//check user exist
	_, err := i.repository.FindUserByID(ctx, adminID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "admin user not found")
			span.RecordError(err)
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find admin user by id")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	user, err := i.repository.FindUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			span.SetStatus(codes.Error, "user not found")
			span.RecordError(err)
			return usecase.ErrUserNotFound
		}
		span.SetStatus(codes.Error, "failed to find user by id")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	if user.RefGitUserID == enums.AdminRefGitID {
		return usecase.ErrCannotDeleteRootUser
	}

	//get root user
	adminRefGitID := enums.AdminRefGitID
	adminUser, err := i.repository.FindUser(ctx, repository.FindUserFilter{RefGitUserID: &adminRefGitID})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find root user")
		span.RecordError(err)
		return err
	}

	if adminUser.GitlabAccessToken == nil {
		err := errors.New("Gitlab Access Token not found")
		span.SetStatus(codes.Error, "root user Gitlab Access Token not found")
		span.RecordError(err)
		return err
	}

	err = i.repository.Transaction(ctx, func(ctx context.Context) error {
		//delete user's repositories
		if err := i.deleteUserRepos(ctx, user, *adminUser.GitlabAccessToken); err != nil {
			span.SetStatus(codes.Error, "failed to delete user repos")
			span.RecordError(err)
			return err
		}

		//remove user's access from organizations
		if err := i.removeUserAccessFromOrg(ctx, user, *adminUser.GitlabAccessToken); err != nil {
			span.SetStatus(codes.Error, "failed to remove user access from organizations")
			span.RecordError(err)
			return err
		}

		// delete user's keys
		if err := i.deleteUserKeys(ctx, user); err != nil {
			span.SetStatus(codes.Error, "failed to delete user keys")
			span.RecordError(err)
			return err
		}

		//delete user info
		if err := i.deleteUser(ctx, user, *adminUser.GitlabAccessToken); err != nil {
			span.SetStatus(codes.Error, "failed to delete user info")
			span.RecordError(err)
			return err
		}

		return nil
	})
	if err != nil {
		return err
	}

	span.AddEvent("user deleted successfully")
	span.SetStatus(codes.Ok, "user deleted successfully")
	return nil
}

// removeUserAccessFromOrg removes a user's access from all organizations.
// It handles removing the user from GitLab groups and organization memberships.
//
// Parameters:
//   - ctx: Context for the operation
//   - user: User entity to remove access for
//   - token: GitLab access token for API calls
//
// Returns:
//   - error: Any error that occurred during access removal
func (i *impl) removeUserAccessFromOrg(ctx context.Context, user *entities.User, token string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteUser.removeUserAccessFromOrg")
	defer span.End()

	_, orgs, err := i.repository.ListOrganizations(ctx, dto.ListOrganizationsInput{
		UserId: user.ID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list organizations")
		span.RecordError(err)
		return err
	}

	for _, org := range orgs {
		orgGitGroup, err := i.repository.FindOrgGitGroup(ctx, org.ID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization git group")
			span.RecordError(err)
			return err
		}

		groupIds := []int64{
			orgGitGroup.RefGitSpacesID,
			orgGitGroup.RefGitModelsID,
			orgGitGroup.RefGitDatasetsID,
		}

		for _, groupId := range groupIds {
			gitlabMember, err := i.gitlabClient.GetMemberOfGroup(ctx, gitlab.GetMemberOfGroupRequest{
				GroupId: groupId,
				UserId:  user.RefGitUserID,
				Token:   token,
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to get member in gitlab group")
				span.RecordError(err)
			}
			if gitlabMember != nil {
				if err = i.gitlabClient.RemoveUserFromGroup(ctx, gitlab.RemoveUserFromGroupRequest{
					GroupId: groupId,
					UserId:  user.RefGitUserID,
					Token:   token,
				}); err != nil {
					span.SetStatus(codes.Error, "failed to remove user from group in gitlab")
					span.RecordError(err)
					return err
				}
			}
		}
	}

	if err := i.repository.DeleteOrgMembersByUserID(ctx, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete organization members by user id")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user access removed from organizations successfully")
	span.SetStatus(codes.Ok, "user access removed from organizations successfully")
	return nil
}

// deleteUserRepos deletes all repositories owned by a user.
// It handles both personal repositories and organization repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - user: User entity whose repositories to delete
//   - gitlabAccessToken: GitLab access token for API calls
//
// Returns:
//   - error: Any error that occurred during repository deletion
func (i *impl) deleteUserRepos(ctx context.Context, user *entities.User, gitlabAccessToken string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteUser.deleteUserRepos")
	defer span.End()

	//remove repo members in all repos
	if err := i.repository.DeleteRepositoryMemberByUserID(ctx, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete repository members by user id")
		span.RecordError(err)
		return err
	}

	repos, err := i.repository.ListRepositories(ctx, dto.GetRepositoriesInput{
		UserID: user.ID.String(),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repositories")
		span.RecordError(err)
		return err
	}

	for _, repo := range repos {
		//delete in org repos
		if repo.OrgID != nil {
			span.AddEvent(
				"remove user in organization's repository",
				trace.WithAttributes(
					attribute.String("org_id", repo.OrgID.String()),
					attribute.String("repo_name", repo.Name),
				),
			)

			//remove user_id in repository
			if err = i.repository.RemoveUserInRepository(ctx, repository.RemoveUserInRepositoryInput{
				IDs: []uuid.UUID{repo.ID},
			}); err != nil {
				span.SetStatus(codes.Error, "failed to remove user in repository")
				span.RecordError(err)
				return err
			}
			//remove user_id in repo's deployments
			if err := i.repository.RemoveUserInDeployments(ctx, repository.RemoveUserInDeploymentsInput{
				RepoIDs: []uuid.UUID{repo.ID},
			}); err != nil {
				span.SetStatus(codes.Error, "failed to remove user in deployments")
				span.RecordError(err)
				return err
			}

			err = i.removeUserFromGitlab(ctx, span, repo.RefGitRepoID, user.RefGitUserID, gitlabAccessToken)
			if err != nil {
				span.SetStatus(codes.Error, "failed to remove user from gitlab")
				span.RecordError(err)
				return err
			}

			continue
		} else {
			span.AddEvent(
				"remove user's repository",
				trace.WithAttributes(attribute.String("repo_name", repo.Name)),
			)

			err := repo_usecase.DeleteRepository(
				ctx,
				span,
				i.repository,
				&repo,
				i.kubeClient,
				i.workflowUsecase,
				i.komposeUsecase,
				i.gitlabClient,
				gitlabAccessToken,
			)
			if err != nil {
				span.SetStatus(codes.Error, "failed to delete repository")
				span.RecordError(err)
				return err
			}
		}
	}

	span.AddEvent("user repositories deleted successfully")
	span.SetStatus(codes.Ok, "user repositories deleted successfully")
	return nil
}

// removeUserFromGitlab removes a user from a GitLab project.
// It handles removing the user's membership from the project.
//
// Parameters:
//   - ctx: Context for the operation
//   - span: OpenTelemetry span for tracing
//   - refGitRepoID: GitLab repository ID
//   - refGitUserID: GitLab user ID
//   - gitlabAccessToken: GitLab access token for API calls
//
// Returns:
//   - error: Any error that occurred during user removal
func (i *impl) removeUserFromGitlab(ctx context.Context, span trace.Span, refGitRepoID, refGitUserID int64, gitlabAccessToken string) error {
	gitlabMember, err := i.gitlabClient.GetMemberOfProject(ctx, gitlab.GetMemberOfProjectRequest{
		RepoId: refGitRepoID,
		UserId: refGitUserID,
		Token:  gitlabAccessToken,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get member in gitlab project")
		span.RecordError(err)
		return err
	}

	if gitlabMember != nil {
		if err = i.gitlabClient.RemoveUserFromProject(ctx, gitlab.RemoveUserFromProjectRequest{
			RepoId: refGitRepoID,
			UserId: refGitUserID,
			Token:  gitlabAccessToken,
		}); err != nil {
			span.SetStatus(codes.Error, "failed to remove user from project in gitlab")
			span.RecordError(err)
			return err
		}
	}

	return nil
}

// deleteUser deletes a user's data from all systems.
// It handles deletion from GitLab, Supabase, and local database.
//
// Parameters:
//   - ctx: Context for the operation
//   - user: User entity to delete
//   - token: GitLab access token for API calls
//
// Returns:
//   - error: Any error that occurred during user deletion
func (i *impl) deleteUser(ctx context.Context, user *entities.User, token string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteUser.deleteUser")
	defer span.End()

	//delete user git groups
	userGitGroup, err := i.repository.FindUserGitGroup(ctx, user.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to find user git group")
		span.RecordError(err)
		return err
	}

	groupIds := []int64{
		userGitGroup.RefGitSpacesID,
		userGitGroup.RefGitModelsID,
		userGitGroup.RefGitDatasetsID,
	}

	for _, groupId := range groupIds {
		if err := i.gitlabClient.DeleteGroup(ctx, gitlab.DeleteOrganizationRequest{
			Id:    int(groupId),
			Token: token,
		}); err != nil && !errors.Is(err, gitlab.ErrNotFound) {
			span.SetStatus(codes.Error, "failed to delete group in gitlab")
			span.RecordError(err)
			return err
		}
	}

	if err := i.repository.DeleteUserGitGroup(ctx, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete user git group")
		span.RecordError(err)
		return err
	}

	if err := i.repository.DeleteById(ctx, &entities.User{}, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete user entity")
		span.RecordError(err)
		return err
	}

	//delete gitlab user
	if err := i.gitlabClient.DeleteUser(ctx, gitlab.DeleteUserRequest{
		AdminAccessToken: token,
		Id:               user.RefGitUserID,
	}); err != nil && !errors.Is(err, gitlab.ErrNotFound) {
		span.SetStatus(codes.Error, "failed to delete user in gitlab")
		span.RecordError(err)
		return err
	}

	//delete supabase user
	if err := i.supabaseClient.DeleteUser(ctx, supabase.DeleteUserRequest{
		ID: user.ID,
	}); err != nil {
		span.SetStatus(codes.Error, "failed to delete user in supabase")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user info deleted successfully")
	span.SetStatus(codes.Ok, "user info deleted successfully")
	return nil
}

// deleteUserKeys deletes all SSH keys and access tokens associated with a user.
//
// Parameters:
//   - ctx: Context for the operation
//   - user: User entity whose keys to delete
//
// Returns:
//   - error: Any error that occurred during key deletion
func (i *impl) deleteUserKeys(ctx context.Context, user *entities.User) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.user.DeleteUser.deleteUserKeys")
	defer span.End()

	if err := i.repository.DeleteSSHKeysByUserID(ctx, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete ssh keys by user id")
		span.RecordError(err)
		return err
	}

	if err := i.repository.DeleteAllUserAccessToken(ctx, user.ID); err != nil {
		span.SetStatus(codes.Error, "failed to delete all user access tokens")
		span.RecordError(err)
		return err
	}

	span.AddEvent("user keys deleted successfully")
	span.SetStatus(codes.Ok, "user keys deleted successfully")
	return nil
}
