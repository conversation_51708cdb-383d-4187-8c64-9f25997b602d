package kompose_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"k8s.io/client-go/kubernetes/fake"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	gitlabMocks "api-server/internal/gateways/gitlab/mocks"
	repository "api-server/internal/repositories"
	repoMocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase/kompose"
	"api-server/internal/utils"
)

func TestListComposeDeployment(t *testing.T) {
	type dependencies struct {
		repo      *repoMocks.MockRepository
		gitlab    *gitlabMocks.MockGitlabClient
		config    *configs.GlobalConfig
		k8sClient *fake.Clientset
	}

	// Sample entities for testing
	userID := uuid.New()
	orgID := uuid.New()
	repoID := uuid.New()

	sampleUser := &entities.User{
		BaseModel: entities.BaseModel{
			ID: userID,
		},
		Username: "testuser",
	}

	sampleRepo := &entities.Repository{
		BaseModel: entities.BaseModel{
			ID: repoID,
		},
		Name:   "test-repo",
		Type:   enums.RepoType_Composes,
		User:   sampleUser,
		UserID: &userID,
	}

	sampleDeployments := []entities.CustomImageDeployment{
		{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			DeploymentName: "web-service",
			ImageURI:       "nginx:latest",
			NodeName:       "cpu",
			Port:           8080,
			UserID:         &userID,
			ProxyBodySize:  100,
			ComposePorts:   []string{"80:80", "443:443"},
			Volumes:        []string{"/data:/app/data"},
			RestartPolicy:  utils.Ptr("always"),
			DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
			RepoID:         &repoID,
			User:           sampleUser,
			Repo:           sampleRepo,
		},
		{
			BaseModel: entities.BaseModel{
				ID:        uuid.New(),
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
			DeploymentName: "db-service",
			ImageURI:       "postgres:13",
			NodeName:       "cpu",
			Port:           5432,
			UserID:         &userID,
			ProxyBodySize:  50,
			ComposePorts:   []string{"5432:5432"},
			Volumes:        []string{"/var/lib/postgresql/data"},
			RestartPolicy:  utils.Ptr("unless-stopped"),
			DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
			RepoID:         &repoID,
			User:           sampleUser,
			Repo:           sampleRepo,
		},
	}

	tests := []struct {
		name           string
		input          dto.ListComposeDeploymentInput
		setupMocks     func(*dependencies)
		expectedResult []dto.ComposeService
		expectedTotal  int64
		expectedError  error
	}{
		{
			name: "successful list with user filter",
			input: dto.ListComposeDeploymentInput{
				UserID:         &userID,
				Page:           1,
				PerPage:        10,
				OrderBy:        enums.OrderByColumn_CreatedAt,
				OrderDirection: enums.OrderByDirection_Desc,
			},
			setupMocks: func(d *dependencies) {
				// Mock ListECRDeployment - success
				d.repo.On("ListECRDeployment", mock.Anything,
					types.Pagination{PageNo: 1, PageSize: 10},
					types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc},
					repository.ListECRDeploymentInput{
						UserID:         &userID,
						OrgID:          nil,
						Search:         nil,
						DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
					},
				).Return(sampleDeployments, int64(2), nil)
			},
			expectedResult: []dto.ComposeService{
				{
					ID:            sampleDeployments[0].ID,
					Name:          "web-service",
					Author:        "testuser",
					Image:         "nginx:latest",
					Ports:         []string{"80:80", "443:443"},
					Volumes:       []string{"/data:/app/data"},
					Status:        "Running", // This will be set by QueryDeploymentStatus
					RestartPolicy: "always",
					NodeName:      "cpu",
					ProxyBodySize: 100,
					CreatedAt:     sampleDeployments[0].CreatedAt,
					UpdatedAt:     sampleDeployments[0].UpdatedAt,
				},
				{
					ID:            sampleDeployments[1].ID,
					Name:          "db-service",
					Author:        "testuser",
					Image:         "postgres:13",
					Ports:         []string{"5432:5432"},
					Volumes:       []string{"/var/lib/postgresql/data"},
					Status:        "Running", // This will be set by QueryDeploymentStatus
					RestartPolicy: "unless-stopped",
					NodeName:      "cpu",
					ProxyBodySize: 50,
					CreatedAt:     sampleDeployments[1].CreatedAt,
					UpdatedAt:     sampleDeployments[1].UpdatedAt,
				},
			},
			expectedTotal: 2,
			expectedError: nil,
		},
		{
			name: "successful list with org filter",
			input: dto.ListComposeDeploymentInput{
				OrgID:          &orgID,
				Page:           1,
				PerPage:        5,
				OrderBy:        enums.OrderByColumn_UpdatedAt,
				OrderDirection: enums.OrderByDirection_Asc,
			},
			setupMocks: func(d *dependencies) {
				// Mock ListECRDeployment - success with org filter
				d.repo.On("ListECRDeployment", mock.Anything,
					types.Pagination{PageNo: 1, PageSize: 5},
					types.OrderBy{enums.OrderByColumn_UpdatedAt: enums.OrderByDirection_Asc},
					repository.ListECRDeploymentInput{
						UserID:         nil,
						OrgID:          &orgID,
						Search:         nil,
						DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
					},
				).Return([]entities.CustomImageDeployment{sampleDeployments[0]}, int64(1), nil)
			},
			expectedResult: []dto.ComposeService{
				{
					ID:            sampleDeployments[0].ID,
					Name:          "web-service",
					Author:        "testuser",
					Image:         "nginx:latest",
					Ports:         []string{"80:80", "443:443"},
					Volumes:       []string{"/data:/app/data"},
					Status:        "Running",
					RestartPolicy: "always",
					NodeName:      "cpu",
					ProxyBodySize: 100,
					CreatedAt:     sampleDeployments[0].CreatedAt,
					UpdatedAt:     sampleDeployments[0].UpdatedAt,
				},
			},
			expectedTotal: 1,
			expectedError: nil,
		},
		{
			name: "successful list with search filter",
			input: dto.ListComposeDeploymentInput{
				UserID:         &userID,
				Search:         utils.Ptr("web"),
				Page:           1,
				PerPage:        10,
				OrderBy:        enums.OrderByColumn_CreatedAt,
				OrderDirection: enums.OrderByDirection_Desc,
			},
			setupMocks: func(d *dependencies) {
				// Mock ListECRDeployment - success with search filter
				d.repo.On("ListECRDeployment", mock.Anything,
					types.Pagination{PageNo: 1, PageSize: 10},
					types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc},
					repository.ListECRDeploymentInput{
						UserID:         &userID,
						OrgID:          nil,
						Search:         utils.Ptr("web"),
						DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
					},
				).Return([]entities.CustomImageDeployment{sampleDeployments[0]}, int64(1), nil)
			},
			expectedResult: []dto.ComposeService{
				{
					ID:            sampleDeployments[0].ID,
					Name:          "web-service",
					Author:        "testuser",
					Image:         "nginx:latest",
					Ports:         []string{"80:80", "443:443"},
					Volumes:       []string{"/data:/app/data"},
					Status:        "Running",
					RestartPolicy: "always",
					NodeName:      "cpu",
					ProxyBodySize: 100,
					CreatedAt:     sampleDeployments[0].CreatedAt,
					UpdatedAt:     sampleDeployments[0].UpdatedAt,
				},
			},
			expectedTotal: 1,
			expectedError: nil,
		},
		{
			name: "empty result",
			input: dto.ListComposeDeploymentInput{
				UserID:         &userID,
				Page:           1,
				PerPage:        10,
				OrderBy:        enums.OrderByColumn_CreatedAt,
				OrderDirection: enums.OrderByDirection_Desc,
			},
			setupMocks: func(d *dependencies) {
				// Mock ListECRDeployment - empty result
				d.repo.On("ListECRDeployment", mock.Anything,
					types.Pagination{PageNo: 1, PageSize: 10},
					types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc},
					repository.ListECRDeploymentInput{
						UserID:         &userID,
						OrgID:          nil,
						Search:         nil,
						DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
					},
				).Return([]entities.CustomImageDeployment{}, int64(0), nil)
			},
			expectedResult: []dto.ComposeService{},
			expectedTotal:  0,
			expectedError:  nil,
		},
		{
			name: "repository error",
			input: dto.ListComposeDeploymentInput{
				UserID:         &userID,
				Page:           1,
				PerPage:        10,
				OrderBy:        enums.OrderByColumn_CreatedAt,
				OrderDirection: enums.OrderByDirection_Desc,
			},
			setupMocks: func(d *dependencies) {
				// Mock ListECRDeployment - error
				d.repo.On("ListECRDeployment", mock.Anything,
					types.Pagination{PageNo: 1, PageSize: 10},
					types.OrderBy{enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc},
					repository.ListECRDeploymentInput{
						UserID:         &userID,
						OrgID:          nil,
						Search:         nil,
						DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
					},
				).Return(nil, int64(0), errors.New("database error"))
			},
			expectedResult: nil,
			expectedTotal:  0,
			expectedError:  errors.New("failed to list ECR deployments: database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockRepo := repoMocks.NewMockRepository(t)
			mockGitlab := gitlabMocks.NewMockGitlabClient(t)
			fakeK8sClient := fake.NewSimpleClientset()
			config := &configs.GlobalConfig{
				Space: &configs.SpaceConfig{
					SpaceDomain: "example.com",
				},
			}

			dep := dependencies{
				repo:      mockRepo,
				gitlab:    mockGitlab,
				k8sClient: fakeK8sClient,
				config:    config,
			}

			tt.setupMocks(&dep)

			// Create usecase instance
			usecase := kompose.New(dep.config, dep.repo, dep.k8sClient, dep.gitlab)

			// Execute test
			result, total, err := usecase.ListComposeDeployment(context.Background(), tt.input)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, result)
				assert.Equal(t, int64(0), total)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedTotal, total)
				assert.Len(t, result, len(tt.expectedResult))

				// Compare each service (excluding dynamic fields like URL and Status)
				for i, expectedService := range tt.expectedResult {
					if i < len(result) {
						assert.Equal(t, expectedService.ID, result[i].ID)
						assert.Equal(t, expectedService.Name, result[i].Name)
						assert.Equal(t, expectedService.Author, result[i].Author)
						assert.Equal(t, expectedService.Image, result[i].Image)
						assert.Equal(t, expectedService.Ports, result[i].Ports)
						assert.Equal(t, expectedService.Volumes, result[i].Volumes)
						assert.Equal(t, expectedService.RestartPolicy, result[i].RestartPolicy)
						assert.Equal(t, expectedService.NodeName, result[i].NodeName)
						assert.Equal(t, expectedService.ProxyBodySize, result[i].ProxyBodySize)
						assert.Equal(t, expectedService.CreatedAt, result[i].CreatedAt)
						assert.Equal(t, expectedService.UpdatedAt, result[i].UpdatedAt)

						// URL and Status are set dynamically, so we just check they're not empty for successful cases
						if tt.expectedError == nil && len(tt.expectedResult) > 0 {
							assert.NotEmpty(t, result[i].URL)
							// Status might be empty if k8s query fails, so we don't assert on it
						}
					}
				}
			}

			// Verify all mocks were called as expected
			mockRepo.AssertExpectations(t)
			mockGitlab.AssertExpectations(t)
		})
	}
}
