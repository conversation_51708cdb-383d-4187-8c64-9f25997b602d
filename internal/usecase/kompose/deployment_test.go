package kompose_test

import (
	"context"
	"encoding/base64"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	gitlabMocks "api-server/internal/gateways/gitlab/mocks"
	repoMocks "api-server/internal/repositories/mocks"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/kompose"
	"api-server/internal/utils"
)

func TestCreateComposeDeployment(t *testing.T) {
	type dependencies struct {
		repo   *repoMocks.MockRepository
		gitlab *gitlabMocks.MockGitlabClient
		config *configs.GlobalConfig
	}

	// Sample docker-compose content for testing
	sampleDockerCompose := `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      app: web
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: testdb
    volumes:
      - db_data:/var/lib/postgresql/data
volumes:
  db_data:
`

	tests := []struct {
		name          string
		userID        uuid.UUID
		repoID        types.RepoID
		input         dto.StartDeploymentRequest
		setupMocks    func(*dependencies)
		expectedError error
		expectSuccess bool
	}{
		{
			name:   "repository not found",
			userID: uuid.New(),
			repoID: *types.NewRepoID(enums.RepoType_Composes, "test-org", "test-repo"),
			input: dto.StartDeploymentRequest{
				Revision: utils.Ptr("main"),
			},
			setupMocks: func(d *dependencies) {
				// Mock FindRepository - not found
				d.repo.On("FindRepository", mock.Anything, mock.MatchedBy(func(filter interface{}) bool {
					return true
				})).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedError: usecase.ErrRepositoryNotFound,
			expectSuccess: false,
		},
		{
			name:   "docker-compose file not found",
			userID: uuid.New(),
			repoID: *types.NewRepoID(enums.RepoType_Composes, "test-org", "test-repo"),
			input: dto.StartDeploymentRequest{
				Revision: utils.Ptr("main"),
			},
			setupMocks: func(d *dependencies) {
				userID := uuid.New()
				repo := &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Name:         "test-repo",
					RefGitRepoID: 123,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: userID,
						},
						Username: "testuser",
					},
					UserID: &userID,
				}

				// Mock FindRepository - success
				d.repo.On("FindRepository", mock.Anything, mock.MatchedBy(func(filter interface{}) bool {
					return true
				})).Return(repo, nil)

				// Mock FindUserByID - success
				user := &entities.User{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Username:          "testuser",
					GitlabAccessToken: utils.Ptr("test-token"),
					Role:              enums.UserRole_User,
				}
				d.repo.On("FindUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(user, nil)

				// Mock GetFileFromRepo - not found
				d.gitlab.On("GetFileFromRepo", mock.Anything, mock.MatchedBy(func(req gitlab.GetFileRequest) bool {
					return req.ProjectId == 123 && req.FilePath == "docker-compose.yaml"
				})).Return(nil, gitlab.ErrNotFound)
			},
			expectedError: usecase.ErrDockerComposeFileNotFound,
			expectSuccess: false,
		},
		{
			name:   "invalid docker-compose content",
			userID: uuid.New(),
			repoID: *types.NewRepoID(enums.RepoType_Composes, "test-org", "test-repo"),
			input: dto.StartDeploymentRequest{
				Revision: utils.Ptr("main"),
			},
			setupMocks: func(d *dependencies) {
				userID := uuid.New()
				repo := &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Name:         "test-repo",
					RefGitRepoID: 123,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: userID,
						},
						Username: "testuser",
					},
					UserID: &userID,
				}

				// Mock FindRepository - success
				d.repo.On("FindRepository", mock.Anything, mock.MatchedBy(func(filter interface{}) bool {
					return true
				})).Return(repo, nil)

				// Mock FindUserByID - success
				user := &entities.User{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Username:          "testuser",
					GitlabAccessToken: utils.Ptr("test-token"),
					Role:              enums.UserRole_User,
				}
				d.repo.On("FindUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(user, nil)

				// Mock GetFileFromRepo - return invalid docker-compose
				invalidDockerCompose := "invalid: yaml: content"
				encodedContent := base64.StdEncoding.EncodeToString([]byte(invalidDockerCompose))
				d.gitlab.On("GetFileFromRepo", mock.Anything, mock.MatchedBy(func(req gitlab.GetFileRequest) bool {
					return req.ProjectId == 123 && req.FilePath == "docker-compose.yaml"
				})).Return(&gitlab.GetFileResponse{
					Content: encodedContent,
				}, nil)
			},
			expectedError: usecase.ErrInvalidInput,
			expectSuccess: false,
		},
		{
			name:   "transaction failure",
			userID: uuid.New(),
			repoID: *types.NewRepoID(enums.RepoType_Composes, "test-org", "test-repo"),
			input: dto.StartDeploymentRequest{
				Revision: utils.Ptr("main"),
			},
			setupMocks: func(d *dependencies) {
				userID := uuid.New()
				repo := &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Name:         "test-repo",
					RefGitRepoID: 123,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: userID,
						},
						Username: "testuser",
					},
					UserID: &userID,
				}

				// Mock FindRepository - success
				d.repo.On("FindRepository", mock.Anything, mock.MatchedBy(func(filter interface{}) bool {
					return true
				})).Return(repo, nil)

				// Mock FindUserByID - success
				user := &entities.User{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Username:          "testuser",
					GitlabAccessToken: utils.Ptr("test-token"),
					Role:              enums.UserRole_User,
				}
				d.repo.On("FindUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(user, nil)

				// Mock GetFileFromRepo - return valid docker-compose
				encodedContent := base64.StdEncoding.EncodeToString([]byte(sampleDockerCompose))
				d.gitlab.On("GetFileFromRepo", mock.Anything, mock.MatchedBy(func(req gitlab.GetFileRequest) bool {
					return req.ProjectId == 123 && req.FilePath == "docker-compose.yaml"
				})).Return(&gitlab.GetFileResponse{
					Content: encodedContent,
				}, nil)

				// Mock GetEnv - return empty env
				d.repo.On("GetEnv", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(nil, gorm.ErrRecordNotFound)

				// Mock Transaction - return error to avoid executing the full transaction
				// This simulates a transaction failure which is a valid test case
				d.repo.On("Transaction", mock.Anything, mock.AnythingOfType("func(context.Context) error")).Return(errors.New("transaction failed"))
			},
			expectedError: errors.New("transaction failed"),
			expectSuccess: false,
		},
		{
			name:   "user not found",
			userID: uuid.New(),
			repoID: *types.NewRepoID(enums.RepoType_Composes, "test-org", "test-repo"),
			input: dto.StartDeploymentRequest{
				Revision: utils.Ptr("main"),
			},
			setupMocks: func(d *dependencies) {
				userID := uuid.New()
				repo := &entities.Repository{
					BaseModel: entities.BaseModel{
						ID: uuid.New(),
					},
					Name:         "test-repo",
					RefGitRepoID: 123,
					User: &entities.User{
						BaseModel: entities.BaseModel{
							ID: userID,
						},
						Username: "testuser",
					},
					UserID: &userID,
				}

				// Mock FindRepository - success
				d.repo.On("FindRepository", mock.Anything, mock.MatchedBy(func(filter interface{}) bool {
					return true
				})).Return(repo, nil)

				// Mock FindUserByID - user not found
				d.repo.On("FindUserByID", mock.Anything, mock.AnythingOfType("uuid.UUID")).Return(nil, gorm.ErrRecordNotFound)
			},
			expectedError: gorm.ErrRecordNotFound,
			expectSuccess: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockRepo := repoMocks.NewMockRepository(t)
			mockGitlab := gitlabMocks.NewMockGitlabClient(t)
			config := &configs.GlobalConfig{}

			dep := dependencies{
				repo:   mockRepo,
				gitlab: mockGitlab,
				config: config,
			}

			tt.setupMocks(&dep)

			// Create usecase instance
			usecase := kompose.New(dep.config, dep.repo, nil, dep.gitlab)

			// Execute test
			result, err := usecase.CreateComposeDeployment(context.Background(), tt.userID, tt.repoID, tt.input)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError.Error())
				assert.Nil(t, result)
			} else if tt.expectSuccess {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "success", result.Message)
			}

			// Verify all mocks were called as expected
			mockRepo.AssertExpectations(t)
			mockGitlab.AssertExpectations(t)
		})
	}
}
