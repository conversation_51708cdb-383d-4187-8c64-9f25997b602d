package kompose

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateMemoryValue(t *testing.T) {
	tests := []struct {
		name        string
		value       string
		expectError bool
	}{
		{"valid Mi suffix", "256Mi", false},
		{"valid Gi suffix", "1Gi", false},
		{"valid Ki suffix", "512Ki", false},
		{"valid M suffix", "256M", false},
		{"valid G suffix", "1G", false},
		{"valid K suffix", "512K", false},
		{"valid without suffix", "1024", false},
		{"valid decimal with Mi", "1.5Gi", false},
		{"valid decimal without suffix", "1.5", false},
		{"invalid format", "256XYZ", true},
		{"empty value", "", true},
		{"invalid characters", "256Mi-invalid", true},
		{"negative value", "-256Mi", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateMemoryValue(tt.value)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateCPUValue(t *testing.T) {
	tests := []struct {
		name        string
		value       string
		expectError bool
	}{
		{"valid millicores", "100m", false},
		{"valid cores", "1", false},
		{"valid decimal cores", "1.5", false},
		{"valid decimal millicores", "100.5m", false},
		{"valid zero", "0", false},
		{"valid zero millicores", "0m", false},
		{"invalid format", "100x", true},
		{"empty value", "", true},
		{"invalid characters", "100m-invalid", true},
		{"negative value", "-100m", true},
		{"invalid decimal", "1.5.5", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateCPUValue(tt.value)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestValidateResourceValue(t *testing.T) {
	tests := []struct {
		name        string
		labelKey    string
		value       string
		expectError bool
	}{
		{"valid memory request", "volvo.deployment.request.memory", "256Mi", false},
		{"valid memory limit", "volvo.deployment.limit.memory", "512Mi", false},
		{"valid cpu request", "volvo.deployment.request.cpu", "100m", false},
		{"valid cpu limit", "volvo.deployment.limit.cpu", "200m", false},
		{"invalid memory value", "volvo.deployment.request.memory", "256XYZ", true},
		{"invalid cpu value", "volvo.deployment.request.cpu", "100x", true},
		{"empty value", "volvo.deployment.request.memory", "", true},
		{"unknown resource type", "volvo.deployment.request.unknown", "100", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateResourceValue(tt.labelKey, tt.value)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
