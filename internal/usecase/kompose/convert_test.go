package kompose_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"api-server/internal/usecase/kompose"
)

func TestConvertDockerComposeToKubernetes(t *testing.T) {
	tests := []struct {
		name                    string
		input                   kompose.ConvertDockerComposeInput
		expectError             bool
		errorContains           string
		expectedManifestCount   int
		expectedResourceTypes   []string
		expectedTotalServices   int
		expectedWarningsCount   int
		validateManifestContent func(t *testing.T, manifests []kompose.KubernetesManifest)
	}{
		{
			name: "valid simple docker-compose with single service",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    environment:
      - ENV=production
`,
				Namespace: "test-namespace",
			},
			expectError:           false,
			expectedManifestCount: 0, // Don't check exact count, kompose behavior varies
			expectedResourceTypes: []string{"Deployment", "Service"},
			expectedTotalServices: 1,
			validateManifestContent: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				// Verify we have the expected resource types
				kinds := make(map[string]bool)
				for _, manifest := range manifests {
					kinds[manifest.Kind] = true
					assert.NotEmpty(t, manifest.Name, "Manifest name should not be empty")
					assert.NotEmpty(t, manifest.Content, "Manifest content should not be empty")
					assert.Contains(t, manifest.Content, "apiVersion", "Manifest should contain apiVersion")
					assert.Contains(t, manifest.Content, "kind", "Manifest should contain kind")
					assert.Contains(t, manifest.Content, "metadata", "Manifest should contain metadata")
				}
				assert.True(t, kinds["Deployment"], "Should contain Deployment")
				assert.True(t, kinds["Service"], "Should contain Service")
			},
		},
		{
			name: "docker-compose with multiple services",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
  api:
    image: node:16
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=myapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
`,
				Namespace: "multi-service",
			},
			expectError:           false,
			expectedManifestCount: 0, // Don't check exact count, kompose behavior varies
			expectedResourceTypes: []string{"Deployment", "Service"},
			expectedTotalServices: 2, // Only web and api have ports, so they get services
			validateManifestContent: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				deploymentCount := 0
				serviceCount := 0
				for _, manifest := range manifests {
					switch manifest.Kind {
					case "Deployment":
						deploymentCount++
					case "Service":
						serviceCount++
					}
				}
				assert.Equal(t, 3, deploymentCount, "Should have 3 deployments")
				assert.Equal(t, 2, serviceCount, "Should have 2 services")
			},
		},
		{
			name: "docker-compose with volumes",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  app:
    image: nginx:latest
    ports:
      - "8080:80"
    volumes:
      - data:/var/lib/data
      - ./config:/etc/config:ro
volumes:
  data:
`,
				VolumeType:     "persistentVolumeClaim",
				PVCRequestSize: "2Gi",
			},
			expectError:           false,
			expectedManifestCount: 0, // Don't check exact count, kompose behavior varies
			expectedResourceTypes: []string{"Deployment", "Service", "PersistentVolumeClaim"},
			expectedTotalServices: 1,
			validateManifestContent: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				hasPVC := false
				for _, manifest := range manifests {
					if manifest.Kind == "PersistentVolumeClaim" {
						hasPVC = true
						break
					}
				}
				assert.True(t, hasPVC, "Should contain PersistentVolumeClaim")
			},
		},
		{
			name: "docker-compose with network policies enabled",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
  api:
    image: node:16
    ports:
      - "3000:3000"
`,
				GenerateNetworkPolicies: true,
			},
			expectError:           false,
			expectedManifestCount: 0, // Don't check exact count, kompose behavior varies
			expectedResourceTypes: []string{"Deployment", "Service", "NetworkPolicy"},
			expectedTotalServices: 2,
			validateManifestContent: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				hasNetworkPolicy := false
				for _, manifest := range manifests {
					if manifest.Kind == "NetworkPolicy" {
						hasNetworkPolicy = true
						break
					}
				}
				assert.True(t, hasNetworkPolicy, "Should contain NetworkPolicy")
			},
		},

		{
			name: "docker-compose with custom namespace",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
`,
				Namespace: "custom-namespace",
			},
			expectError:           false,
			expectedManifestCount: 0, // Don't check exact count, kompose creates Namespace resource too
			expectedResourceTypes: []string{"Deployment", "Service", "Namespace"},
			expectedTotalServices: 1,
			validateManifestContent: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				for _, manifest := range manifests {
					// Check that namespace is properly set in the manifest content
					assert.Contains(t, manifest.Content, "namespace", "Manifest should contain namespace")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a new usecase instance for each test
			usecase := kompose.New(nil, nil, nil, nil)

			// Execute the function
			response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), tt.input)

			// Validate error expectations
			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				assert.Nil(t, response)
				return
			}

			// Validate successful response
			require.NoError(t, err)
			require.NotNil(t, response)

			// Validate manifest count
			if tt.expectedManifestCount > 0 {
				assert.Len(t, response.Manifests, tt.expectedManifestCount, "Unexpected number of manifests")
			} else {
				// Just ensure we have some manifests
				assert.NotEmpty(t, response.Manifests, "Should generate at least one manifest")
			}

			// Validate resource types
			if len(tt.expectedResourceTypes) > 0 {
				actualTypes := make(map[string]bool)
				for _, manifest := range response.Manifests {
					actualTypes[manifest.Kind] = true
				}
				for _, expectedType := range tt.expectedResourceTypes {
					assert.True(t, actualTypes[expectedType], "Expected resource type %s not found", expectedType)
				}
			}

			// Validate summary
			assert.Equal(t, tt.expectedTotalServices, response.Summary.TotalServices, "Unexpected total services count")
			assert.NotNil(t, response.Summary.GeneratedResources, "Generated resources should not be nil")

			// Validate warnings count
			if tt.expectedWarningsCount > 0 {
				assert.Len(t, response.Summary.Warnings, tt.expectedWarningsCount, "Unexpected warnings count")
			}

			// Run custom validation if provided
			if tt.validateManifestContent != nil {
				tt.validateManifestContent(t, response.Manifests)
			}

			// Validate that all manifests have required fields
			for _, manifest := range response.Manifests {
				assert.NotEmpty(t, manifest.Kind, "Manifest kind should not be empty")
				assert.NotEmpty(t, manifest.Name, "Manifest name should not be empty")
				assert.NotEmpty(t, manifest.Content, "Manifest content should not be empty")
			}
		})
	}
}

func TestConvertDockerComposeToKubernetes_DefaultValues(t *testing.T) {
	t.Run("default replicas behavior", func(t *testing.T) {
		input := kompose.ConvertDockerComposeInput{
			DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
`,
		}

		usecase := kompose.New(nil, nil, nil, nil)
		response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)

		require.NoError(t, err)
		require.NotNil(t, response)

		// The function should use default replica count of 1
		// This is tested indirectly through successful conversion
		assert.NotEmpty(t, response.Manifests)
	})

	t.Run("default volume type behavior", func(t *testing.T) {
		input := kompose.ConvertDockerComposeInput{
			DockerComposeContent: `
version: '3.8'
services:
  app:
    image: nginx:latest
    volumes:
      - data:/var/lib/data
volumes:
  data:
`,
			// VolumeType not specified, should default to persistentVolumeClaim
		}

		usecase := kompose.New(nil, nil, nil, nil)
		response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)

		require.NoError(t, err)
		require.NotNil(t, response)

		// Should create PVC by default
		hasPVC := false
		for _, manifest := range response.Manifests {
			if manifest.Kind == "PersistentVolumeClaim" {
				hasPVC = true
				break
			}
		}
		assert.True(t, hasPVC, "Should create PersistentVolumeClaim by default")
	})
}

func TestConvertDockerComposeToKubernetes_EdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		input         kompose.ConvertDockerComposeInput
		expectError   bool
		errorContains string
		description   string
	}{
		{
			name: "docker-compose with very long service name",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  very-long-service-name-that-might-cause-issues-in-kubernetes-because-it-exceeds-normal-limits:
    image: nginx:latest
    ports:
      - "8080:80"
`,
			},
			expectError: false, // Kompose should handle this or truncate
			description: "Should handle long service names gracefully",
		},
		{
			name: "docker-compose with special characters in service name",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  web_service:
    image: nginx:latest
    ports:
      - "8080:80"
`,
			},
			expectError: false,
			description: "Should handle underscores in service names",
		},
		{
			name: "docker-compose with no ports exposed",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  worker:
    image: redis:latest
    environment:
      - REDIS_PASSWORD=secret
`,
			},
			expectError: false,
			description: "Should handle services without exposed ports",
		},
		{
			name: "docker-compose with complex environment variables",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  app:
    image: node:16
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************/myapp
      - API_KEY=very-secret-key-with-special-chars!@#$%
      - MULTILINE_VAR=line1\nline2\nline3
`,
			},
			expectError: false,
			description: "Should handle complex environment variables",
		},
		{
			name: "docker-compose with different volume types",
			input: kompose.ConvertDockerComposeInput{
				DockerComposeContent: `
version: '3.8'
services:
  app:
    image: nginx:latest
    volumes:
      - named_volume:/data
      - ./local_dir:/app
      - /host/path:/host
volumes:
  named_volume:
`,
				VolumeType: "emptyDir",
			},
			expectError: false,
			description: "Should handle different volume mount types",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecase := kompose.New(nil, nil, nil, nil)
			response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), tt.input)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, response)
				assert.NotEmpty(t, response.Manifests, "Should generate at least one manifest")
			}
		})
	}
}

func TestConvertDockerComposeToKubernetes_VolumeTypes(t *testing.T) {
	baseDockerCompose := `
version: '3.8'
services:
  app:
    image: nginx:latest
    volumes:
      - data:/var/lib/data
volumes:
  data:
`

	tests := []struct {
		name           string
		volumeType     string
		pvcRequestSize string
		expectError    bool
		validateFunc   func(t *testing.T, manifests []kompose.KubernetesManifest)
	}{
		{
			name:           "persistentVolumeClaim with default size",
			volumeType:     "persistentVolumeClaim",
			pvcRequestSize: "",
			expectError:    false,
			validateFunc: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				hasPVC := false
				for _, manifest := range manifests {
					if manifest.Kind == "PersistentVolumeClaim" {
						hasPVC = true
						break
					}
				}
				assert.True(t, hasPVC, "Should contain PersistentVolumeClaim")
			},
		},
		{
			name:           "persistentVolumeClaim with custom size",
			volumeType:     "persistentVolumeClaim",
			pvcRequestSize: "5Gi",
			expectError:    false,
			validateFunc: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				hasPVC := false
				for _, manifest := range manifests {
					if manifest.Kind == "PersistentVolumeClaim" {
						hasPVC = true
						// Could check for size in content if needed
						break
					}
				}
				assert.True(t, hasPVC, "Should contain PersistentVolumeClaim")
			},
		},
		{
			name:        "emptyDir volume type",
			volumeType:  "emptyDir",
			expectError: false,
			validateFunc: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				// EmptyDir volumes don't create separate resources
				// They're configured within the deployment
				hasDeployment := false
				for _, manifest := range manifests {
					if manifest.Kind == "Deployment" {
						hasDeployment = true
						break
					}
				}
				assert.True(t, hasDeployment, "Should contain Deployment")
			},
		},
		{
			name:        "hostPath volume type",
			volumeType:  "hostPath",
			expectError: false,
			validateFunc: func(t *testing.T, manifests []kompose.KubernetesManifest) {
				// HostPath volumes don't create separate resources
				hasDeployment := false
				for _, manifest := range manifests {
					if manifest.Kind == "Deployment" {
						hasDeployment = true
						break
					}
				}
				assert.True(t, hasDeployment, "Should contain Deployment")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := kompose.ConvertDockerComposeInput{
				DockerComposeContent: baseDockerCompose,
				VolumeType:           tt.volumeType,
				PVCRequestSize:       tt.pvcRequestSize,
			}

			usecase := kompose.New(nil, nil, nil, nil)
			response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
				assert.NotEmpty(t, response.Manifests)

				if tt.validateFunc != nil {
					tt.validateFunc(t, response.Manifests)
				}
			}
		})
	}
}

func TestConvertDockerComposeToKubernetes_FileHandling(t *testing.T) {
	t.Run("temporary file cleanup", func(t *testing.T) {
		input := kompose.ConvertDockerComposeInput{
			DockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
`,
		}

		usecase := kompose.New(nil, nil, nil, nil)

		// Execute multiple times to ensure temp files are cleaned up
		for i := 0; i < 3; i++ {
			response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)
			require.NoError(t, err)
			require.NotNil(t, response)
			assert.NotEmpty(t, response.Manifests)
		}

		// Note: We can't easily test temp file cleanup directly since it's handled
		// by defer statements, but multiple successful runs indicate proper cleanup
	})
}

func TestConvertDockerComposeToKubernetes_RealWorldExamples(t *testing.T) {
	tests := []struct {
		name                  string
		dockerComposeContent  string
		expectedResourceCount int
		description           string
	}{
		{
			name: "WordPress with MySQL",
			dockerComposeContent: `
version: '3.8'
services:
  wordpress:
    image: wordpress:latest
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    depends_on:
      - db
  db:
    image: mysql:5.7
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - db_data:/var/lib/mysql
volumes:
  db_data:
`,
			expectedResourceCount: 0, // Don't check exact count, kompose behavior varies
			description:           "WordPress with MySQL database",
		},
		{
			name: "Simple web application",
			dockerComposeContent: `
version: '3.8'
services:
  web:
    image: nginx:latest
    ports:
      - "80:80"
    volumes:
      - ./html:/usr/share/nginx/html:ro
`,
			expectedResourceCount: 0, // Don't check exact count, kompose behavior varies
			description:           "Simple nginx web server",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := kompose.ConvertDockerComposeInput{
				DockerComposeContent: tt.dockerComposeContent,
				VolumeType:           "persistentVolumeClaim",
				PVCRequestSize:       "1Gi",
			}

			usecase := kompose.New(nil, nil, nil, nil)
			response, err := usecase.ConvertDockerComposeToKubernetes(context.Background(), input)

			require.NoError(t, err, tt.description)
			require.NotNil(t, response)

			// Validate we got some resources
			if tt.expectedResourceCount > 0 {
				assert.Len(t, response.Manifests, tt.expectedResourceCount,
					"Expected %d resources for %s", tt.expectedResourceCount, tt.description)
			} else {
				assert.NotEmpty(t, response.Manifests, "Should generate at least one manifest for %s", tt.description)
			}

			// Validate all manifests are properly formed
			for _, manifest := range response.Manifests {
				assert.NotEmpty(t, manifest.Kind, "Kind should not be empty")
				assert.NotEmpty(t, manifest.Name, "Name should not be empty")
				assert.NotEmpty(t, manifest.Content, "Content should not be empty")
				assert.Contains(t, manifest.Content, "apiVersion", "Should contain apiVersion")
				assert.Contains(t, manifest.Content, "kind", "Should contain kind")
				assert.Contains(t, manifest.Content, "metadata", "Should contain metadata")
			}

			// Validate summary
			assert.NotNil(t, response.Summary.GeneratedResources)
			assert.GreaterOrEqual(t, response.Summary.TotalServices, 1, "Should have at least one service")
		})
	}
}
