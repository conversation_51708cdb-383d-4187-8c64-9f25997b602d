package hardware

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/sync/errgroup"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	ecrUsecase "api-server/internal/usecase/ecr"
	repoUsecase "api-server/internal/usecase/repository"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

const (
	spaceNamespace       = "space"
	customImageNamespace = "custom-image"
	replicaSetKind       = "ReplicaSet"
	deploymentKind       = "Deployment"
)

// HardwareUsecase defines the interface for managing hardware resources.
// It provides methods for listing and managing hardware configurations.
type HardwareUsecase interface {
	// ListHardware retrieves a list of hardware resources based on the request parameters.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request parameters for listing hardware
	//
	// Returns:
	//   - *dto.ListHardwareResponse: The list of hardware resources
	//   - error: Any error that occurred during retrieval
	ListHardware(ctx context.Context, req dto.ListHardwareRequest) (*dto.ListHardwareResponse, error)

	// ListGPUNodes implements the HardwareUsecase interface for retrieving a list of GPU nodes.
	// It fetches GPU node information from Kubernetes including node details, GPU specifications,
	// and running deployments on each node.
	//
	// Parameters:
	//   - ctx: Context for the operation
	//
	// Returns:
	//   - *dto.ListGPUNodesResponse: Response containing the list of GPU nodes and their details
	//   - error: Any error that occurred during retrieval
	ListGPUNodes(ctx context.Context, short bool) (*dto.ListGPUNodesResponse, error)
	// CheckHardwareExist(ctx context.Context, id uuid.UUID) (bool, error)
}

var _ HardwareUsecase = (*impl)(nil)

type impl struct {
	repository repository.Repository
	kubeClient kubernetes.Interface
}

// New creates a new instance of the hardware usecase implementation.
// It initializes the usecase with the required repository dependency.
//
// Parameters:
//   - repository: Repository interface for hardware data access
//
// Returns:
//   - *impl: New instance of the hardware usecase
func New(repository repository.Repository, kubeClient kubernetes.Interface) *impl {
	return &impl{
		repository: repository,
		kubeClient: kubeClient,
	}
}

// ListHardware retrieves a paginated list of hardware resources.
// It includes total count and supports sorting and filtering.
// The function includes OpenTelemetry tracing for monitoring the operation.
//
// Parameters:
//   - ctx: Context for the operation
//   - req: Request containing pagination and sorting parameters
//
// Returns:
//   - *dto.ListHardwareResponse: Paginated list of hardware resources
//   - error: Any error that occurred during retrieval
func (u *impl) ListHardware(ctx context.Context, req dto.ListHardwareRequest) (*dto.ListHardwareResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.ListHardware")
	defer span.End()

	total, err := u.repository.CountHardware(ctx)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		return nil, usecase.ErrInternal
	}

	pagination := types.Pagination{
		PageNo:   req.Paginate.Page,
		PageSize: req.Paginate.PerPage,
	}
	orderBy := types.OrderBy{req.Paginate.OrderBy: req.Paginate.Sort}
	resp, err := u.repository.ListHardware(ctx, pagination, orderBy)
	if err != nil {
		return nil, usecase.ErrInternal
	}

	data := dto.FromManyEntities[entities.Hardware, dto.Hardware](resp)
	result := dto.ListHardwareResponse{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(total),
			PageNo:   pagination.PageNo,
			PageSize: pagination.PageSize,
		},
	}

	return &result, nil
}

// ListGPUNodes implements the HardwareUsecase interface for retrieving a list of GPU nodes.
// It fetches GPU node information from Kubernetes including node details, GPU specifications,
// and running deployments on each node.
//
// Parameters:
//   - ctx: Context for the operation
//   - short: A boolean flag to indicate if short processing is required
//
// Returns:
//   - *dto.ListGPUNodesResponse: Response containing the list of GPU nodes and their details
//   - error: Any error that occurred during retrieval
func (i *impl) ListGPUNodes(ctx context.Context, short bool) (*dto.ListGPUNodesResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.ListGPUNodes")
	defer span.End()

	k8sNodes, err := i.kubeClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: "nvidia.com/gpu.product",
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list nodes from k8s cluster")
		span.RecordError(err)
		return nil, usecase.ErrInternal
	}

	// Get all pods on 'space' namespace once for efficiency
	spaceK8sPods, err := i.kubeClient.CoreV1().Pods(spaceNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list pods from k8s cluster")
		span.RecordError(err)
		return nil, err
	}

	// Get all pods on 'custom-image' namespace once for efficiency
	customImageK8sPods, err := i.kubeClient.CoreV1().Pods(customImageNamespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list pods from k8s cluster")
		span.RecordError(err)
		return nil, err
	}

	// Initialize CPU node
	nodes := []dto.GPUNode{
		{
			Name:     "CPU",
			NodeName: "cpu",
		},
	}
	gpuNodes, err := i.collectSpaceDeploymentOnNode(ctx, k8sNodes, spaceK8sPods, customImageK8sPods, short)
	if err != nil {
		span.SetStatus(codes.Error, "failed to collect deployments")
		span.RecordError(err)
		return nil, err
	}
	nodes = append(nodes, gpuNodes...)

	span.AddEvent("list gpu node successfully")
	span.SetStatus(codes.Ok, "list gpu node successfully")
	return &dto.ListGPUNodesResponse{
		Data: &nodes,
	}, nil
}

// collectSpaceDeploymentOnNode collects GPU deployment information for each node.
// It processes nodes and their associated pods to gather GPU usage information concurrently.
//
// Parameters:
//   - ctx: Context for the operation
//   - nodes: List of Kubernetes nodes
//   - pods: List of Kubernetes pods
//   - short: A boolean flag to indicate if short processing is required
//
// Returns:
//   - []dto.GPUNode: List of GPU nodes with their deployment information
//   - error: Any error that occurred during collection
func (i *impl) collectSpaceDeploymentOnNode(ctx context.Context, nodes *v1.NodeList, spacePods, customImagePods *v1.PodList, short bool) ([]dto.GPUNode, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.collectSpaceDeploymentOnNode")
	defer span.End()

	gpuNodes := make([]dto.GPUNode, 0, len(nodes.Items))
	gpuNodesChan := make(chan dto.GPUNode, len(nodes.Items))

	g, ctx := errgroup.WithContext(ctx)
	g.SetLimit(8)

	for _, node := range nodes.Items {
		node := node
		g.Go(func() error {
			gpuNode, err := i.processNode(ctx, node, spacePods, customImagePods, short)
			if err != nil {
				return err
			}
			gpuNodesChan <- gpuNode
			return nil
		})
	}

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to process nodes")
		span.RecordError(err)
		return nil, err
	}
	close(gpuNodesChan) // must close the channel for the for loop below to work

	// Collect results
	for gpuNode := range gpuNodesChan {
		gpuNodes = append(gpuNodes, gpuNode)
	}

	return gpuNodes, nil
}

// processNode processes a single node and its associated pods to gather GPU information.
func (i *impl) processNode(ctx context.Context, node v1.Node, spacePods, customImagePods *v1.PodList, short bool) (dto.GPUNode, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.processNode")
	defer span.End()

	label := node.Labels[usecase.GPULabelKey]
	gpuQty := node.Status.Allocatable[usecase.GPUResourceKey]
	qtyInt, _ := gpuQty.AsInt64()

	gpuMemGB := getGPUMemory(label)
	deployments, err := i.collectNodeDeployments(ctx, node.Name, spacePods, customImagePods, short)
	if err != nil {
		span.SetStatus(codes.Error, "failed to collect node deployments")
		span.RecordError(err)
		return dto.GPUNode{}, err
	}
	return dto.GPUNode{
		NodeName:    node.Name,
		Name:        utils.TrimNodeName(label),
		GPUModel:    utils.TrimNodeName(label),
		GPUCount:    qtyInt,
		GPUMemoryGB: gpuMemGB,
		Deployments: deployments,
	}, nil
}

// collectNodeDeployments collects deployment information for pods running on a specific node.
// It iterates over the provided pods and filters those running on the specified nodeName.
// For each relevant pod, it processes it to extract deployment-related GPU information.
//
// Parameters:
//   - ctx: The context for the operation.
//   - nodeName: The name of the node to collect deployments for.
//   - pods: A list of all pods to filter and process.
//   - short: A boolean flag to indicate if short processing is required.
//
// Returns:
//   - []dto.DeploymentGPUInfo: A slice of deployment GPU information for the node.
//   - error: An error if processing any pod fails.
//
// nolint: gocognit, cyclop
func (i *impl) collectNodeDeployments(ctx context.Context, nodeName string, spacePods, customImagePods *v1.PodList, short bool) ([]dto.DeploymentGPUInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.collectNodeDeployments")
	defer span.End()

	var deployments []dto.DeploymentGPUInfo

	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)

	for _, pod := range spacePods.Items {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				if pod.Spec.NodeName != nodeName {
					return nil
				}

				deploymentInfo, err := i.processSpacePod(ctx, pod, short)
				if err != nil {
					span.SetStatus(codes.Error, "failed to process pod")
					span.RecordError(err)
					return err
				}
				if deploymentInfo != nil {
					deployments = append(deployments, *deploymentInfo)
				}

				return nil
			})
		}
	}

	for _, pod := range customImagePods.Items {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				if pod.Spec.NodeName != nodeName {
					return nil
				}

				deploymentInfo, err := i.processCustomImagePod(ctx, pod, short)
				if err != nil {
					span.SetStatus(codes.Error, "failed to process pod")
					span.RecordError(err)
					return err
				}
				if deploymentInfo != nil {
					deployments = append(deployments, *deploymentInfo)
				}

				return nil
			})
		}
	}

	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to get last commit for files")
		span.RecordError(err)
		return nil, err
	}

	return deployments, nil
}

// processSpacePod processes a single pod to extract deployment information.
// It retrieves the deployment name and space ID, finds the corresponding repository,
// determines the namespace, and gathers container resource requests.
//
// Parameters:
//   - ctx: The context for the operation.
//   - pod: The Kubernetes pod to process.
//
// Returns:
//   - *dto.DeploymentGPUInfo: A pointer to the deployment GPU information, or nil if not a relevant deployment.
//   - error: An error if any step in processing fails (e.g., finding repository, getting namespace).
func (i *impl) processSpacePod(ctx context.Context, pod v1.Pod, short bool) (*dto.DeploymentGPUInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.processSpacePod")
	defer span.End()

	deploymentName, spaceId, err := i.getDeploymentInfo(ctx, pod)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment info")
		span.RecordError(err)
		return nil, err
	}
	if deploymentName == "" {
		return nil, nil
	}

	repo, err := i.repository.FindRepository(ctx, repository.RepositoryFilter{
		Id: &spaceId,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}

	namespace, err := getNamespace(ctx, i, repo)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get namespace")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to get namespace: %w", err)
	}

	gpuRequest, memoryBytesRequest, cpuRequest := getContainerRequestResource(pod.Spec.Containers)

	// return early if short is true
	if short {
		return &dto.DeploymentGPUInfo{
			Name:               deploymentName,
			RepoID:             types.NewRepoID(repo.Type, *namespace, repo.Name),
			GPURequest:         gpuRequest,
			MemoryBytesRequest: memoryBytesRequest,
			CPURequest:         cpuRequest,
		}, nil
	}

	var user dto.User
	if repo.User != nil {
		user = user.FromEntity(*repo.User)
	}

	status, err := repoUsecase.MapDeploymentStatus(ctx, i.kubeClient, repo.Deployment.Status, repo.ID.String())
	if err != nil {
		span.SetStatus(codes.Error, "failed to map container status")
		span.RecordError(err)
		return nil, err
	}

	return &dto.DeploymentGPUInfo{
		ID:                 &repo.ID,
		Name:               deploymentName,
		RepoID:             types.NewRepoID(repo.Type, *namespace, repo.Name),
		GPURequest:         gpuRequest,
		MemoryBytesRequest: memoryBytesRequest,
		CPURequest:         cpuRequest,
		DeploymentType:     enums.DeploymentType_Space,
		Status:             status,
		User:               &user,
		UpdatedAt:          &repo.UpdatedAt,
	}, nil
}

func (i *impl) processCustomImagePod(ctx context.Context, pod v1.Pod, short bool) (*dto.DeploymentGPUInfo, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.processCustomImagePod")
	defer span.End()

	deploymentName, ecrDeploymentID, err := i.getDeploymentInfo(ctx, pod)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment info")
		span.RecordError(err)
		return nil, err
	}
	if deploymentName == "" {
		return nil, nil
	}

	gpuRequest, memoryBytesRequest, cpuRequest := getContainerRequestResource(pod.Spec.Containers)

	// return early if short is true
	if short {
		return &dto.DeploymentGPUInfo{
			Name:               deploymentName,
			GPURequest:         gpuRequest,
			MemoryBytesRequest: memoryBytesRequest,
			CPURequest:         cpuRequest,
		}, nil
	}

	ecrDeployment, err := i.repository.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &ecrDeploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to get repository: %w", err)
	}

	user := dto.User{}
	if ecrDeployment.User != nil {
		user = user.FromEntity(*ecrDeployment.User)
	}

	status, err := ecrUsecase.QueryDeploymentStatus(ctx, i.kubeClient, ecrDeployment.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to query deployment status")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to query deployment status: %w", err)
	}

	return &dto.DeploymentGPUInfo{
		ID:                 &ecrDeployment.ID,
		Name:               deploymentName,
		GPURequest:         gpuRequest,
		MemoryBytesRequest: memoryBytesRequest,
		CPURequest:         cpuRequest,
		Status:             status,
		User:               &user,
		UpdatedAt:          &ecrDeployment.UpdatedAt,
		DeploymentType:     enums.DeploymentType_ECR,
	}, nil
}

// getDeploymentInfo extracts deployment name and space ID from a pod's owner references.
// It traverses the owner references to find the parent Deployment and parses its name
// to derive the space ID (assuming a naming convention like "space-<uuid>").
//
// Parameters:
//   - ctx: The context for the operation.
//   - pod: The Kubernetes pod whose owner references are to be inspected.
//
// Returns:
//   - string: The deployment name in the format "namespace/deployment-name".
//   - uuid.UUID: The parsed space ID.
//   - error: An error if parsing the UUID from the resource name fails. Returns ( "", uuid.Nil, nil) if no relevant Deployment owner is found.
func (i *impl) getDeploymentInfo(ctx context.Context, pod v1.Pod) (string, uuid.UUID, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.getDeploymentInfo")
	defer span.End()

	for _, owner := range pod.OwnerReferences {
		if owner.Kind != replicaSetKind {
			continue
		}

		rs, err := i.kubeClient.AppsV1().ReplicaSets(pod.Namespace).Get(ctx, owner.Name, metav1.GetOptions{})
		if err != nil {
			continue
		}

		for _, rsOwner := range rs.OwnerReferences {
			if rsOwner.Kind == deploymentKind {
				deploymentName := fmt.Sprintf("%s/%s", pod.Namespace, rsOwner.Name)
				spaceId, err := uuid.Parse(decodeResourceName(rsOwner.Name))
				if err != nil {
					span.SetStatus(codes.Error, "failed to parse UUID from resource name")
					span.RecordError(err)
					return "", uuid.Nil, err
				}
				return deploymentName, spaceId, nil
			}
		}
	}

	return "", uuid.Nil, nil
}

// getContainerRequestResource calculates the total resource requests for all containers in a pod.
// It sums up GPU, memory, and CPU requests across all containers.
//
// Parameters:
//   - containers: List of containers in the pod
//
// Returns:
//   - int64: Total GPU requests
//   - int64: Total memory requests in bytes
//   - int64: Total CPU requests
func getContainerRequestResource(containers []v1.Container) (int64, int64, int64) {
	var gpuRequest, memoryBytesRequest, cpuRequest int64
	for _, c := range containers {
		if gpuReq, ok := c.Resources.Requests[usecase.GPUResourceKey]; ok {
			gpuCount, _ := gpuReq.AsInt64()
			gpuRequest += gpuCount
		}
		if memReq, ok := c.Resources.Requests["memory"]; ok {
			memBytes, _ := memReq.AsInt64()
			memoryBytesRequest += memBytes
		}
		if cpuReq, ok := c.Resources.Requests["cpu"]; ok {
			cpuCount, _ := cpuReq.AsInt64()
			cpuRequest += cpuCount
		}
	}
	return gpuRequest, memoryBytesRequest, cpuRequest
}

// getGPUMemory returns the memory capacity in GB for a given GPU model.
// It maps GPU model labels to their corresponding memory specifications.
//
// Parameters:
//   - model: String representing the GPU model label
//
// Returns:
//   - int: Memory capacity in GB for the specified GPU model
func getGPUMemory(model string) int {
	switch model {
	case "Tesla-T4-16GB":
		return 16
	case "A10G-24GB":
		return 24
	case "A100-80GB":
		return 80
	case "L40S-48GB":
		return 48
	default:
		// TODO: parse from string
		return 0
	}
}

// decodeResourceName extracts the space ID from a Kubernetes resource name.
// It removes the "space-" or "ecr-" prefix if present.
//
// Parameters:
//   - resourceName: String representing the Kubernetes resource name
//
// Returns:
//   - string: The decoded resource name without the prefix
func decodeResourceName(resourceName string) string {
	if len(resourceName) >= 6 {
		if resourceName[:6] == "space-" {
			return resourceName[6:]
		}
		if resourceName[:4] == "ecr-" {
			return resourceName[4:]
		}
	}
	return resourceName
}

// getNamespace retrieves the namespace for a repository based on its ownership.
// It determines the namespace from either organization path or username.
//
// Parameters:
//   - ctx: Context for the operation
//   - u: Hardware usecase implementation
//   - repo: Repository entity to get namespace for
//
// Returns:
//   - *string: Pointer to the namespace string
//   - error: Any error that occurred during retrieval
func getNamespace(ctx context.Context, u *impl, repo *entities.Repository) (*string, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.getNamespace")
	defer span.End()

	var namespace string
	if repo.OrgID != nil {
		organization, err := u.repository.FindOrganizationByID(ctx, *repo.OrgID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find organization")
			span.RecordError(err)
			return nil, err
		}

		namespace = organization.PathName
	} else if repo.UserID != nil {
		repoUser, err := u.repository.FindUserByID(ctx, *repo.UserID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to find user by id")
			span.RecordError(err)
			return nil, err
		}

		namespace = repoUser.Username
	}

	span.SetStatus(codes.Ok, "get namespace successfully")
	return &namespace, nil
}

// func (u *impl) CheckHardwareExist(ctx context.Context, id uuid.UUID) (bool, error) {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.hardware.CheckHardwareExist")
// 	defer span.End()
//
// 	_, err := u.repository.FindHardwareByID(ctx, id)
// 	if err != nil {
// 		span.SetStatus(codes.Error, err.Error())
// 		span.RecordError(err)
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			return false, usecase.ErrHardwareNotFound
// 		}
// 		return false, usecase.ErrInternal
// 	}
//
// 	return true, nil
// }
