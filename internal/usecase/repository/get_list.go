package repository

import (
	"context"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

// GetAllRepositories implements the RepositoryUsecase interface for retrieving multiple repositories.
// It supports pagination, filtering, and sorting of repository data.
//
// Parameters:
//   - ctx: Context for the operation
//   - input: Input data containing list parameters including pagination and filtering options
//
// Returns:
//   - *dto.GetRepositoriesOutput: List of repository information with pagination metadata
//   - error: Any error that occurred during retrieval
func (u *impl) GetAllRepositories(ctx context.Context, input dto.GetRepositoriesInput) (*dto.GetRepositoriesOutput, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetAllRepositories")
	defer span.End()

	count, err := u.repo.CountRepositories(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to count repositories")
		span.RecordError(err)
		return nil, err
	}

	repos, err := u.repo.ListRepositories(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repositories")
		span.RecordError(err)
		return nil, err
	}

	data := dto.FromManyEntities[entities.Repository, dto.Repository](repos)

	// deployment status
	err = getDeploymentStatus(ctx, u.repo, u.kubeClient, input.RepositoryType, repos, data)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get deployment status")
		span.RecordError(err)
		return nil, err
	}

	//tags
	for i := range len(data) {
		//Pre-sign URL
		if data[i].Avatar != nil {
			span.AddEvent("start generating repository avatar")
			repoImage, err := u.aws.GenPreSignUrl(ctx, *data[i].Avatar)
			if err != nil {
				span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
				span.RecordError(err)
				return nil, err
			}

			data[i].Avatar = &repoImage
		}

		if data[i].User != nil && data[i].User.Avatar != nil {
			if data[i].User.Avatar != nil {
				span.AddEvent("start generating user avatar")
				repoImage, err := u.aws.GenPreSignUrl(ctx, *data[i].User.Avatar)
				if err != nil {
					span.SetStatus(codes.Error, "failed to generate presigned url for user avatar")
					span.RecordError(err)
					return nil, err
				}

				data[i].User.Avatar = &repoImage
			}
		}

		tags, err := u.repo.ListRepoTagsByQueryModel(ctx, data[i].Metadata)
		if err != nil {
			span.SetStatus(codes.Error, "failed to list repository tags")
			span.RecordError(err)
			return nil, err
		}

		data[i].Tags = dto.FromManyEntities[entities.Tag, dto.Tag](tags)
	}

	span.AddEvent("list repositories successfully")
	span.SetStatus(codes.Ok, "list repositories successfully")
	return &dto.GetRepositoriesOutput{
		Data: &data,
		Pagination: &dto.Pagination{
			Total:    int(count),
			PageNo:   input.Paginate.Page,
			PageSize: input.Paginate.PerPage,
		},
	}, nil
}

// getDeploymentStatus retrieves and updates the deployment status for a list of repositories.
// It handles concurrent status checks for space and compose repositories.
//
// Parameters:
//   - ctx: Context for the operation
//   - repo: Repository data access layer
//   - kubeClient: Kubernetes client interface
//   - repoType: Type of repository
//   - repos: List of repository entities
//   - data: List of repository DTOs to update
//
// Returns:
//   - error: Any error that occurred during status retrieval
func getDeploymentStatus(
	ctx context.Context,
	repo repository.Repository,
	kubeClient kubernetes.Interface,
	repoType enums.RepoType,
	repos []entities.Repository,
	data []dto.Repository,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.getDeploymentStatus")
	defer span.End()

	// deployment status for Space repository
	if repoType == enums.RepoType_Spaces {
		g, gCtx := errgroup.WithContext(ctx)
		g.SetLimit(8)

		for i := range len(repos) {
			select {
			case <-gCtx.Done():
				break
			default:
				if repos[i].Deployment == nil {
					data[i].Deployment = &dto.DeploymentStatus{
						Status: string(enums.ArgoWorkflowStatus_NotRunning),
					}
					continue
				}

				g.Go(func() error {
					status, err := MapDeploymentStatus(ctx, kubeClient, repos[i].Deployment.Status, repos[i].ID.String())
					if err != nil {
						span.AddEvent("failed to get deployment status", trace.WithAttributes(
							attribute.String("deployment_status", string(repos[i].Deployment.Status)),
							attribute.String("repo_id", repos[i].ID.String()),
						))
						return err
					}

					data[i].Deployment = status
					return nil
				})
			}
		}

		if err := g.Wait(); err != nil {
			span.SetStatus(codes.Error, "failed to get deployment status")
			span.RecordError(err)
			return err
		}
	}

	// deployment status for Compose repository
	if repoType == enums.RepoType_Composes {
		for i := range len(repos) {
			deployments, _, err := repo.ListECRDeployment(ctx, types.Pagination{
				PageNo:   1,
				PageSize: 100,
			}, types.OrderBy{
				enums.OrderByColumn_CreatedAt: enums.OrderByDirection_Desc,
			}, repository.ListECRDeploymentInput{
				RepoID:         &repos[i].ID,
				DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Compose),
			})
			if err != nil {
				span.SetStatus(codes.Error, "failed to list deployments")
				span.RecordError(err)
				return err
			}

			aggregatedStatus, err := getComposeDeploymentStatus(ctx, kubeClient, &repos[i], deployments)
			if err != nil {
				span.SetStatus(codes.Error, "failed to get deployment status")
				span.RecordError(err)
				return err
			}

			data[i].Deployment = &dto.DeploymentStatus{
				Status: aggregatedStatus,
			}
		}
	}

	return nil
}
