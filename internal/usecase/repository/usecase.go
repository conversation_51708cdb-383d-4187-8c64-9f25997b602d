package repository

import (
	"context"
	"net/http"

	"github.com/google/uuid"
	"k8s.io/client-go/kubernetes"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/gateways/aws"
	"api-server/internal/gateways/gitlab"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase/kompose"
	"api-server/internal/usecase/workflow"
)

// RepoUsecase defines the interface for repository-related operations.
// It provides methods for managing repositories, their members, deployments, and access tokens.
type RepoUsecase interface {
	// CreateRepository creates a new repository with the specified input data.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for creating the repository
	//
	// Returns:
	//   - *dto.RepositoryCreateResponse: The created repository data
	//   - error: Any error that occurred during creation
	CreateRepository(ctx context.Context, input dto.RepositoryCreateInput) (*dto.RepositoryCreateResponse, error)

	// DeleteRepository deletes a repository based on the input criteria.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the repository
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteRepository(ctx context.Context, input dto.DeleteRepositoryInput) error

	// UpdateRepository updates an existing repository with new data.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for updating the repository
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateRepository(ctx context.Context, input dto.UpdateRepositoryInput) error

	// GetFileFromRepository retrieves a file from the repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving the file
	//
	// Returns:
	//   - *dto.GetFileFromRepositoryOutput: The file data
	//   - error: Any error that occurred during retrieval
	GetFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*dto.GetFileFromRepositoryOutput, error)

	// GetHeaderRawFileFromRepository retrieves the raw file headers from the repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving the file headers
	//
	// Returns:
	//   - *http.Response: The HTTP response containing file headers
	//   - error: Any error that occurred during retrieval
	GetHeaderRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error)

	// GetRawFileFromRepository retrieves the raw file content from the repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving the raw file
	//
	// Returns:
	//   - *http.Response: The HTTP response containing raw file content
	//   - error: Any error that occurred during retrieval
	GetRawFileFromRepository(ctx context.Context, input dto.GetFileFromRepositoryInput) (*http.Response, error)

	// GetAllRepositories retrieves a list of repositories based on input criteria.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving repositories
	//
	// Returns:
	//   - *dto.GetRepositoriesOutput: The list of repositories
	//   - error: Any error that occurred during retrieval
	GetAllRepositories(ctx context.Context, input dto.GetRepositoriesInput) (*dto.GetRepositoriesOutput, error)

	// GetRepoInfo retrieves information about a specific repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving repository info
	//
	// Returns:
	//   - *dto.GetRepositoryOutput: The repository information
	//   - error: Any error that occurred during retrieval
	GetRepoInfo(ctx context.Context, input dto.GetRepositoryInput) (*dto.GetRepositoryOutput, error)

	// UploadAvatarRepo uploads an avatar for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for uploading the avatar
	//
	// Returns:
	//   - string: The URL of the uploaded avatar
	//   - error: Any error that occurred during upload
	UploadAvatarRepo(ctx context.Context, input dto.UploadRepositoryAvatarInput) (string, error)

	// DeleteAvatarRepo deletes a repository's avatar.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the avatar
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteAvatarRepo(ctx context.Context, input dto.DeleteRepositoryAvatarInput) error

	// ListRepoBranches retrieves a list of branches for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving branches
	//
	// Returns:
	//   - []dto.RepositoryBranchInfo: The list of branch information
	//   - error: Any error that occurred during retrieval
	ListRepoBranches(ctx context.Context, input dto.GetRepositoryBranchesInput) ([]dto.RepositoryBranchInfo, error)

	// GetSingleRepoBranch retrieves information about a specific branch.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving branch info
	//
	// Returns:
	//   - *dto.RepositoryBranchInfo: The branch information
	//   - error: Any error that occurred during retrieval
	GetSingleRepoBranch(ctx context.Context, input dto.GetSingleRepositoryBranchInput) (*dto.RepositoryBranchInfo, error)

	// ListRepoFiles retrieves a list of files in a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving files
	//
	// Returns:
	//   - []dto.RepositoryFile: The list of file information
	//   - *string: Pointer to the next page URL for pagination
	//   - error: Any error that occurred during retrieval
	ListRepoFiles(ctx context.Context, input dto.GetRepositoryFilesInput) ([]dto.RepositoryFile, *string, error)

	// ListRepoCommits retrieves a list of commits for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving commits
	//
	// Returns:
	//   - *dto.GetRepositoryCommitsOutput: The list of commit information
	//   - error: Any error that occurred during retrieval
	ListRepoCommits(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.GetRepositoryCommitsOutput, error)

	// GetLastRepoCommit retrieves the most recent commit for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving the last commit
	//
	// Returns:
	//   - *dto.RepositoryCommit: The last commit information
	//   - error: Any error that occurred during retrieval
	GetLastRepoCommit(ctx context.Context, input dto.GetRepositoryCommitsInput) (*dto.RepositoryCommit, error)

	// CreateRepoCommit creates a new commit in the repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for creating the commit
	//
	// Returns:
	//   - *dto.RepositoryCommit: The created commit information
	//   - error: Any error that occurred during creation
	CreateRepoCommit(ctx context.Context, input dto.CreateRepositoryCommitInput) (*dto.RepositoryCommit, error)

	// ListRepoContributors retrieves a list of contributors for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving contributors
	//
	// Returns:
	//   - []dto.RepositoryContributor: The list of contributor information
	//   - error: Any error that occurred during retrieval
	ListRepoContributors(ctx context.Context, input dto.GetRepositoryContributorsInput) ([]dto.RepositoryContributor, error)

	// ListRepoMembers retrieves a list of members for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving members
	//
	// Returns:
	//   - *dto.ListRepositoryMembersOutput: The list of member information
	//   - error: Any error that occurred during retrieval
	ListRepoMembers(ctx context.Context, input dto.ListRepositoryMembersInput) (*dto.ListRepositoryMembersOutput, error)

	// RemoveMember removes a member from a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for removing the member
	//
	// Returns:
	//   - error: Any error that occurred during removal
	RemoveMember(ctx context.Context, input dto.RemoveMemberRepositoryInput) error

	// GetMember retrieves information about a specific repository member.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving member info
	//
	// Returns:
	//   - *entities.RepoMember: The member information
	//   - error: Any error that occurred during retrieval
	GetMember(ctx context.Context, input dto.GetMemberRepositoryInput) (*entities.RepoMember, error)

	// UpdateMember updates a repository member's information.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for updating the member
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateMember(ctx context.Context, input dto.UpdateMemberRepositoryInput) error

	// StartDeployment starts a deployment for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user starting the deployment
	//   - repoID: The ID of the repository
	//   - input: The input data for starting the deployment
	//
	// Returns:
	//   - *dto.StartDeploymentResponse: The deployment information
	//   - error: Any error that occurred during deployment
	StartDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID, input dto.StartDeploymentRequest) (*dto.StartDeploymentResponse, error)

	// TerminateDeployment terminates a deployment for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user terminating the deployment
	//   - repoID: The ID of the repository
	//
	// Returns:
	//   - error: Any error that occurred during termination
	TerminateDeployment(ctx context.Context, userID uuid.UUID, repoID types.RepoID) error

	// RestartDeployment restarts a deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for restarting the deployment
	//
	// Returns:
	//   - error: Any error that occurred during restart
	RestartDeployment(ctx context.Context, req dto.RestartDeploymentRequest) error

	// StopDeployment stops a deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for stopping the deployment
	//
	// Returns:
	//   - error: Any error that occurred during stop
	StopDeployment(ctx context.Context, input dto.StopDeploymentRequest) error

	// ListDeployment retrieves a list of deployments.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving deployments
	//
	// Returns:
	//   - *dto.ListDeploymentResponse: The list of deployment information
	//   - error: Any error that occurred during retrieval
	ListDeployment(ctx context.Context, input dto.ListDeploymentRequest) (*dto.ListDeploymentResponse, error)

	// UpdateDeploymentStatus updates the status of a deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for updating the status
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateDeploymentStatus(ctx context.Context, req dto.UpdateDeploymentStatusRequest) error

	// GetDeploymentBuildLogs retrieves build logs for a deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for retrieving logs
	//
	// Returns:
	//   - chan dto.GetDeploymentLogsResponse: Channel for receiving log data
	//   - error: Any error that occurred during retrieval
	GetDeploymentBuildLogs(ctx context.Context, req dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)

	// GetPodLogs retrieves logs for a pod.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for retrieving logs
	//
	// Returns:
	//   - chan dto.GetDeploymentLogsResponse: Channel for receiving log data
	//   - error: Any error that occurred during retrieval
	GetPodLogs(ctx context.Context, req dto.GetDeploymentLogsRequest) (chan dto.GetDeploymentLogsResponse, error)

	// GetDeploymentStatus retrieves the status of a deployment.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for retrieving status
	//
	// Returns:
	//   - *dto.GetDeploymentStatusResponse: The deployment status information
	//   - error: Any error that occurred during retrieval
	GetDeploymentStatus(ctx context.Context, req dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)

	// CreateRepoAccessToken creates an access token for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for creating the token
	//
	// Returns:
	//   - *dto.CreateRepoAccessTokenResponse: The created token information
	//   - error: Any error that occurred during creation
	CreateRepoAccessToken(ctx context.Context, req dto.CreateRepoAccessTokenRequest) (*dto.CreateRepoAccessTokenResponse, error)

	// DeleteRepoAccessToken deletes an access token for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - userID: The ID of the user deleting the token
	//   - repoID: The ID of the repository
	//   - accessTokenID: The ID of the access token to delete
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteRepoAccessToken(ctx context.Context, userID uuid.UUID, repoID types.RepoID, accessTokenID uuid.UUID) error

	// ListRepoAccessToken retrieves a list of access tokens for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - req: The request data for retrieving tokens
	//
	// Returns:
	//   - *dto.ListRepoAccessTokenResponse: The list of token information
	//   - error: Any error that occurred during retrieval
	ListRepoAccessToken(ctx context.Context, req dto.ListRepoAccessTokenRequest) (*dto.ListRepoAccessTokenResponse, error)

	// InviteRepoMember invites a member to a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for inviting the member
	//
	// Returns:
	//   - error: Any error that occurred during invitation
	InviteRepoMember(ctx context.Context, input dto.InviteRepoMemberInput) error

	// InviteMultipleRepoMembers invites multiple members to a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for inviting the members
	//
	// Returns:
	//   - error: Any error that occurred during invitation
	InviteMultipleRepoMembers(ctx context.Context, input dto.InviteRepoMembersInput) error

	// ListRepoTags retrieves a list of tags for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving tags
	//
	// Returns:
	//   - *dto.ListRepoTagsOutput: The list of tag information
	//   - error: Any error that occurred during retrieval
	ListRepoTags(ctx context.Context, input dto.ListRepoTagsInput) (*dto.ListRepoTagsOutput, error)

	// CreateRepoTag creates a new tag for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for creating the tag
	//
	// Returns:
	//   - error: Any error that occurred during creation
	CreateRepoTag(ctx context.Context, input dto.CreateRepoTagInput) error

	// DeleteRepoTag deletes a tag from a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for deleting the tag
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteRepoTag(ctx context.Context, input dto.DeleteRepoTagInput) error

	// GetRepoTag retrieves information about a specific repository tag.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for retrieving tag info
	//
	// Returns:
	//   - *entities.Tag: The tag information
	//   - error: Any error that occurred during retrieval
	GetRepoTag(ctx context.Context, input dto.GetRepoTagInput) (*entities.Tag, error)

	// UpdateRepoTag updates a repository tag.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for updating the tag
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateRepoTag(ctx context.Context, input dto.UpdateRepoTagInput) error

	// UpdateTagsInRepository updates tags in a repository based on GitLab push event.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The GitLab push event data
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateTagsInRepository(ctx context.Context, input dto.GitLabPushEvent) error

	// ListRepoTemplates retrieves a list of repository templates.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//
	// Returns:
	//   - []dto.RepoTemplate: The list of template information
	//   - error: Any error that occurred during retrieval
	ListRepoTemplates(ctx context.Context) ([]dto.RepoTemplate, error)

	// GetEnv retrieves environment variables for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - repoID: The ID of the repository
	//
	// Returns:
	//   - *dto.GetRepositoryEnvResponse: The environment variables
	//   - error: Any error that occurred during retrieval
	GetEnv(ctx context.Context, repoID types.RepoID) (*dto.GetRepositoryEnvResponse, error)

	// CreateEnv creates environment variables for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - repoID: The ID of the repository
	//   - req: The request data for creating environment variables
	//
	// Returns:
	//   - error: Any error that occurred during creation
	CreateEnv(ctx context.Context, repoID types.RepoID, req dto.CreateRepositoryEnvRequest) error

	// BulkCreateEnv creates multiple environment variables for a repository in a single operation.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - repoID: The ID of the repository
	//   - req: The request data for creating multiple environment variables
	//
	// Returns:
	//   - error: Any error that occurred during creation
	BulkCreateEnv(ctx context.Context, repoID types.RepoID, req dto.BulkCreateRepositoryEnvRequest) error

	// UpdateEnv updates environment variables for a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - repoID: The ID of the repository
	//   - req: The request data for updating environment variables
	//
	// Returns:
	//   - error: Any error that occurred during update
	UpdateEnv(ctx context.Context, repoID types.RepoID, req dto.UpdateRepositoryEnvRequest) error

	// DeleteEnv deletes environment variables from a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - repoID: The ID of the repository
	//   - req: The request data for deleting environment variables
	//
	// Returns:
	//   - error: Any error that occurred during deletion
	DeleteEnv(ctx context.Context, repoID types.RepoID, req dto.DeleteRepositoryEnvRequest) error

	// ArchiveRepo archives a repository.
	//
	// Parameters:
	//   - ctx: The context for the operation
	//   - input: The input data for archiving the repository
	//
	// Returns:
	//   - *http.Response: The HTTP response from the archive operation
	//   - error: Any error that occurred during archiving
	ArchiveRepo(ctx context.Context, input dto.ArchiveRepositoryInput) (*http.Response, error)

	// GetComposeRepoDeploymentStatus retrieves the current status of a compose repository deployment.
	GetComposeRepoDeploymentStatus(ctx context.Context, req dto.GetDeploymentStatusRequest) (*dto.GetDeploymentStatusResponse, error)
	GetListComposeServices(ctx context.Context, req dto.GetComposeServicesRequest) (*dto.GetComposeServicesResponse, error)
}

type impl struct {
	config     *configs.GlobalConfig
	repo       repository.Repository
	gitlab     gitlab.GitlabClient
	workflow   workflow.WorkflowUsecase
	kompose    kompose.KomposeUsecase
	kubeClient kubernetes.Interface
	aws        aws.AWSClient
}

var _ RepoUsecase = (*impl)(nil)

// New creates a new instance of the repository usecase implementation.
// It initializes the usecase with the required dependencies including configuration,
// repository, GitLab client, workflow usecase, Kubernetes client, and AWS client.
//
// Parameters:
//   - config: Global application configuration
//   - repo: Repository interface for data access
//   - gitlab: Client for GitLab API interactions
//   - workflow: Usecase for managing workflows (e.g., Tekton pipelines)
//   - kompose: Usecase for managing Kompose operations
//   - kubeClient: Client for Kubernetes API interactions
//   - aws: Client for AWS services (e.g., S3 for avatars)
//
// Returns:
//   - *impl: New instance of the repository usecase
func New(
	config *configs.GlobalConfig,
	repo repository.Repository,
	gitlab gitlab.GitlabClient,
	workflow workflow.WorkflowUsecase,
	kompose kompose.KomposeUsecase,
	kubeClient kubernetes.Interface,
	aws aws.AWSClient,
) *impl {
	return &impl{
		config:     config,
		repo:       repo,
		gitlab:     gitlab,
		kompose:    kompose,
		workflow:   workflow,
		kubeClient: kubeClient,
		aws:        aws,
	}
}
