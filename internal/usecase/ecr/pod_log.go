package ecr

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"api-server/internal/dto"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/kompose"
	repoUsecase "api-server/internal/usecase/repository"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

func (i *impl) GetPodLogs(ctx context.Context, deploymentID uuid.UUID) (chan dto.GetDeploymentLogsResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.repository.GetPodLogs")
	defer span.End()

	deployment, err := i.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrNoDeployment
		}
		return nil, fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	var resourceNamespace, labelSelector string
	var listOptions metav1.ListOptions
	if *deployment.DeploymentType == types.CustomImageDeploymentType_Compose {
		resourceNamespace = *deployment.Namespace
		labelSelector = kompose.ComposeServiceLabelSelector(deployment.DeploymentName, deployment.Repo.ID)
		listOptions = metav1.ListOptions{
			LabelSelector: labelSelector,
		}
	} else {
		resourceName := ecrResourceName(deployment.ID.String())
		resourceNamespace = customImageNamespace
		labelSelector = fmt.Sprintf("app=%s", resourceName)
		listOptions = metav1.ListOptions{
			LabelSelector: labelSelector,
		}
	}

	dataChan := make(chan dto.GetDeploymentLogsResponse)

	var pod corev1.Pod
podPolling:
	for {
		select {
		case <-ctx.Done():
			break podPolling
		default:
			podList, err := i.k8sClient.CoreV1().
				Pods(resourceNamespace).
				List(ctx, listOptions)
			if err != nil {
				close(dataChan)
				span.SetAttributes(attribute.String("pod_label_selector", labelSelector))
				span.SetStatus(codes.Error, "failed to list pods")
				span.RecordError(err)
				return nil, err
			}

			if len(podList.Items) == 0 {
				utils.Sleep(ctx, 5*time.Second)
				continue podPolling
			}

			pod = podList.Items[0]
			stopChan := make(chan struct{})
			go repoUsecase.StreamPodLogsRetry(ctx, i.k8sClient, resourceNamespace, pod.Name, dataChan, stopChan)

			break podPolling
		}
	}

	return dataChan, nil
}
