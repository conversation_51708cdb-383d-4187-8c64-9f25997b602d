// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	entities "api-server/internal/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"

	repository "api-server/internal/repositories"

	types "api-server/internal/types"

	uuid "github.com/google/uuid"
)

// MockCustomImageDeploymentRepository is an autogenerated mock type for the CustomImageDeploymentRepository type
type MockCustomImageDeploymentRepository struct {
	mock.Mock
}

type MockCustomImageDeploymentRepository_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCustomImageDeploymentRepository) EXPECT() *MockCustomImageDeploymentRepository_Expecter {
	return &MockCustomImageDeploymentRepository_Expecter{mock: &_m.Mock}
}

// CreateECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockCustomImageDeploymentRepository) CreateECRDeployment(ctx context.Context, input repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateCustomImageDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.CreateCustomImageDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCustomImageDeploymentRepository_CreateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeployment'
type MockCustomImageDeploymentRepository_CreateECRDeployment_Call struct {
	*mock.Call
}

// CreateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.CreateCustomImageDeploymentInput
func (_e *MockCustomImageDeploymentRepository_Expecter) CreateECRDeployment(ctx interface{}, input interface{}) *MockCustomImageDeploymentRepository_CreateECRDeployment_Call {
	return &MockCustomImageDeploymentRepository_CreateECRDeployment_Call{Call: _e.mock.On("CreateECRDeployment", ctx, input)}
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeployment_Call) Run(run func(ctx context.Context, input repository.CreateCustomImageDeploymentInput)) *MockCustomImageDeploymentRepository_CreateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateCustomImageDeploymentInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockCustomImageDeploymentRepository_CreateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeployment_Call) RunAndReturn(run func(context.Context, repository.CreateCustomImageDeploymentInput) (*entities.CustomImageDeployment, error)) *MockCustomImageDeploymentRepository_CreateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// CreateECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockCustomImageDeploymentRepository) CreateECRDeploymentEnv(ctx context.Context, in repository.CreateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for CreateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.CreateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateECRDeploymentEnv'
type MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call struct {
	*mock.Call
}

// CreateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.CreateECRDeploymentEnvInput
func (_e *MockCustomImageDeploymentRepository_Expecter) CreateECRDeploymentEnv(ctx interface{}, in interface{}) *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call {
	return &MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call{Call: _e.mock.On("CreateECRDeploymentEnv", ctx, in)}
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.CreateECRDeploymentEnvInput)) *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.CreateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call) Return(_a0 error) *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.CreateECRDeploymentEnvInput) error) *MockCustomImageDeploymentRepository_CreateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeployment provides a mock function with given fields: ctx, id
func (_m *MockCustomImageDeploymentRepository) DeleteECRDeployment(ctx context.Context, id uuid.UUID) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeployment")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCustomImageDeploymentRepository_DeleteECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeployment'
type MockCustomImageDeploymentRepository_DeleteECRDeployment_Call struct {
	*mock.Call
}

// DeleteECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - id uuid.UUID
func (_e *MockCustomImageDeploymentRepository_Expecter) DeleteECRDeployment(ctx interface{}, id interface{}) *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call {
	return &MockCustomImageDeploymentRepository_DeleteECRDeployment_Call{Call: _e.mock.On("DeleteECRDeployment", ctx, id)}
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call) Run(run func(ctx context.Context, id uuid.UUID)) *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call) Return(_a0 error) *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call) RunAndReturn(run func(context.Context, uuid.UUID) error) *MockCustomImageDeploymentRepository_DeleteECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockCustomImageDeploymentRepository) DeleteECRDeploymentEnv(ctx context.Context, in repository.DeleteECRDeploymentEnv) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.DeleteECRDeploymentEnv) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeploymentEnv'
type MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call struct {
	*mock.Call
}

// DeleteECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.DeleteECRDeploymentEnv
func (_e *MockCustomImageDeploymentRepository_Expecter) DeleteECRDeploymentEnv(ctx interface{}, in interface{}) *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call {
	return &MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call{Call: _e.mock.On("DeleteECRDeploymentEnv", ctx, in)}
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.DeleteECRDeploymentEnv)) *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.DeleteECRDeploymentEnv))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call) Return(_a0 error) *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.DeleteECRDeploymentEnv) error) *MockCustomImageDeploymentRepository_DeleteECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteECRDeploymentsByRepoID provides a mock function with given fields: ctx, repoID, deploymentType
func (_m *MockCustomImageDeploymentRepository) DeleteECRDeploymentsByRepoID(ctx context.Context, repoID uuid.UUID, deploymentType *types.CustomImageDeploymentType) error {
	ret := _m.Called(ctx, repoID, deploymentType)

	if len(ret) == 0 {
		panic("no return value specified for DeleteECRDeploymentsByRepoID")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID, *types.CustomImageDeploymentType) error); ok {
		r0 = rf(ctx, repoID, deploymentType)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteECRDeploymentsByRepoID'
type MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call struct {
	*mock.Call
}

// DeleteECRDeploymentsByRepoID is a helper method to define mock.On call
//   - ctx context.Context
//   - repoID uuid.UUID
//   - deploymentType *types.CustomImageDeploymentType
func (_e *MockCustomImageDeploymentRepository_Expecter) DeleteECRDeploymentsByRepoID(ctx interface{}, repoID interface{}, deploymentType interface{}) *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call {
	return &MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call{Call: _e.mock.On("DeleteECRDeploymentsByRepoID", ctx, repoID, deploymentType)}
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call) Run(run func(ctx context.Context, repoID uuid.UUID, deploymentType *types.CustomImageDeploymentType)) *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID), args[2].(*types.CustomImageDeploymentType))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call) Return(_a0 error) *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call) RunAndReturn(run func(context.Context, uuid.UUID, *types.CustomImageDeploymentType) error) *MockCustomImageDeploymentRepository_DeleteECRDeploymentsByRepoID_Call {
	_c.Call.Return(run)
	return _c
}

// FindECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockCustomImageDeploymentRepository) FindECRDeployment(ctx context.Context, input repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for FindECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.FindECRDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.FindECRDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCustomImageDeploymentRepository_FindECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindECRDeployment'
type MockCustomImageDeploymentRepository_FindECRDeployment_Call struct {
	*mock.Call
}

// FindECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.FindECRDeploymentInput
func (_e *MockCustomImageDeploymentRepository_Expecter) FindECRDeployment(ctx interface{}, input interface{}) *MockCustomImageDeploymentRepository_FindECRDeployment_Call {
	return &MockCustomImageDeploymentRepository_FindECRDeployment_Call{Call: _e.mock.On("FindECRDeployment", ctx, input)}
}

func (_c *MockCustomImageDeploymentRepository_FindECRDeployment_Call) Run(run func(ctx context.Context, input repository.FindECRDeploymentInput)) *MockCustomImageDeploymentRepository_FindECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.FindECRDeploymentInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockCustomImageDeploymentRepository_FindECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindECRDeployment_Call) RunAndReturn(run func(context.Context, repository.FindECRDeploymentInput) (*entities.CustomImageDeployment, error)) *MockCustomImageDeploymentRepository_FindECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// FindOrgDeployments provides a mock function with given fields: ctx, orgID
func (_m *MockCustomImageDeploymentRepository) FindOrgDeployments(ctx context.Context, orgID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, orgID)

	if len(ret) == 0 {
		panic("no return value specified for FindOrgDeployments")
	}

	var r0 []entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)); ok {
		return rf(ctx, orgID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, orgID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, orgID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCustomImageDeploymentRepository_FindOrgDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindOrgDeployments'
type MockCustomImageDeploymentRepository_FindOrgDeployments_Call struct {
	*mock.Call
}

// FindOrgDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - orgID uuid.UUID
func (_e *MockCustomImageDeploymentRepository_Expecter) FindOrgDeployments(ctx interface{}, orgID interface{}) *MockCustomImageDeploymentRepository_FindOrgDeployments_Call {
	return &MockCustomImageDeploymentRepository_FindOrgDeployments_Call{Call: _e.mock.On("FindOrgDeployments", ctx, orgID)}
}

func (_c *MockCustomImageDeploymentRepository_FindOrgDeployments_Call) Run(run func(ctx context.Context, orgID uuid.UUID)) *MockCustomImageDeploymentRepository_FindOrgDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindOrgDeployments_Call) Return(_a0 []entities.CustomImageDeployment, _a1 error) *MockCustomImageDeploymentRepository_FindOrgDeployments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindOrgDeployments_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)) *MockCustomImageDeploymentRepository_FindOrgDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// FindUserDeployments provides a mock function with given fields: ctx, userID
func (_m *MockCustomImageDeploymentRepository) FindUserDeployments(ctx context.Context, userID uuid.UUID) ([]entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, userID)

	if len(ret) == 0 {
		panic("no return value specified for FindUserDeployments")
	}

	var r0 []entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)); ok {
		return rf(ctx, userID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, userID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, userID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCustomImageDeploymentRepository_FindUserDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindUserDeployments'
type MockCustomImageDeploymentRepository_FindUserDeployments_Call struct {
	*mock.Call
}

// FindUserDeployments is a helper method to define mock.On call
//   - ctx context.Context
//   - userID uuid.UUID
func (_e *MockCustomImageDeploymentRepository_Expecter) FindUserDeployments(ctx interface{}, userID interface{}) *MockCustomImageDeploymentRepository_FindUserDeployments_Call {
	return &MockCustomImageDeploymentRepository_FindUserDeployments_Call{Call: _e.mock.On("FindUserDeployments", ctx, userID)}
}

func (_c *MockCustomImageDeploymentRepository_FindUserDeployments_Call) Run(run func(ctx context.Context, userID uuid.UUID)) *MockCustomImageDeploymentRepository_FindUserDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(uuid.UUID))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindUserDeployments_Call) Return(_a0 []entities.CustomImageDeployment, _a1 error) *MockCustomImageDeploymentRepository_FindUserDeployments_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_FindUserDeployments_Call) RunAndReturn(run func(context.Context, uuid.UUID) ([]entities.CustomImageDeployment, error)) *MockCustomImageDeploymentRepository_FindUserDeployments_Call {
	_c.Call.Return(run)
	return _c
}

// ListECRDeployment provides a mock function with given fields: ctx, pagination, order, in
func (_m *MockCustomImageDeploymentRepository) ListECRDeployment(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error) {
	ret := _m.Called(ctx, pagination, order, in)

	if len(ret) == 0 {
		panic("no return value specified for ListECRDeployment")
	}

	var r0 []entities.CustomImageDeployment
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error)); ok {
		return rf(ctx, pagination, order, in)
	}
	if rf, ok := ret.Get(0).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) []entities.CustomImageDeployment); ok {
		r0 = rf(ctx, pagination, order, in)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) int64); ok {
		r1 = rf(ctx, pagination, order, in)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) error); ok {
		r2 = rf(ctx, pagination, order, in)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// MockCustomImageDeploymentRepository_ListECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListECRDeployment'
type MockCustomImageDeploymentRepository_ListECRDeployment_Call struct {
	*mock.Call
}

// ListECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - pagination types.Pagination
//   - order types.OrderBy
//   - in repository.ListECRDeploymentInput
func (_e *MockCustomImageDeploymentRepository_Expecter) ListECRDeployment(ctx interface{}, pagination interface{}, order interface{}, in interface{}) *MockCustomImageDeploymentRepository_ListECRDeployment_Call {
	return &MockCustomImageDeploymentRepository_ListECRDeployment_Call{Call: _e.mock.On("ListECRDeployment", ctx, pagination, order, in)}
}

func (_c *MockCustomImageDeploymentRepository_ListECRDeployment_Call) Run(run func(ctx context.Context, pagination types.Pagination, order types.OrderBy, in repository.ListECRDeploymentInput)) *MockCustomImageDeploymentRepository_ListECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(types.Pagination), args[2].(types.OrderBy), args[3].(repository.ListECRDeploymentInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_ListECRDeployment_Call) Return(_a0 []entities.CustomImageDeployment, _a1 int64, _a2 error) *MockCustomImageDeploymentRepository_ListECRDeployment_Call {
	_c.Call.Return(_a0, _a1, _a2)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_ListECRDeployment_Call) RunAndReturn(run func(context.Context, types.Pagination, types.OrderBy, repository.ListECRDeploymentInput) ([]entities.CustomImageDeployment, int64, error)) *MockCustomImageDeploymentRepository_ListECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeployment provides a mock function with given fields: ctx, input
func (_m *MockCustomImageDeploymentRepository) UpdateECRDeployment(ctx context.Context, input repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error) {
	ret := _m.Called(ctx, input)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeployment")
	}

	var r0 *entities.CustomImageDeployment
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error)); ok {
		return rf(ctx, input)
	}
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentInput) *entities.CustomImageDeployment); ok {
		r0 = rf(ctx, input)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.CustomImageDeployment)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, repository.UpdateECRDeploymentInput) error); ok {
		r1 = rf(ctx, input)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockCustomImageDeploymentRepository_UpdateECRDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeployment'
type MockCustomImageDeploymentRepository_UpdateECRDeployment_Call struct {
	*mock.Call
}

// UpdateECRDeployment is a helper method to define mock.On call
//   - ctx context.Context
//   - input repository.UpdateECRDeploymentInput
func (_e *MockCustomImageDeploymentRepository_Expecter) UpdateECRDeployment(ctx interface{}, input interface{}) *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call {
	return &MockCustomImageDeploymentRepository_UpdateECRDeployment_Call{Call: _e.mock.On("UpdateECRDeployment", ctx, input)}
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call) Run(run func(ctx context.Context, input repository.UpdateECRDeploymentInput)) *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateECRDeploymentInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call) Return(_a0 *entities.CustomImageDeployment, _a1 error) *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call) RunAndReturn(run func(context.Context, repository.UpdateECRDeploymentInput) (*entities.CustomImageDeployment, error)) *MockCustomImageDeploymentRepository_UpdateECRDeployment_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateECRDeploymentEnv provides a mock function with given fields: ctx, in
func (_m *MockCustomImageDeploymentRepository) UpdateECRDeploymentEnv(ctx context.Context, in repository.UpdateECRDeploymentEnvInput) error {
	ret := _m.Called(ctx, in)

	if len(ret) == 0 {
		panic("no return value specified for UpdateECRDeploymentEnv")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, repository.UpdateECRDeploymentEnvInput) error); ok {
		r0 = rf(ctx, in)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateECRDeploymentEnv'
type MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call struct {
	*mock.Call
}

// UpdateECRDeploymentEnv is a helper method to define mock.On call
//   - ctx context.Context
//   - in repository.UpdateECRDeploymentEnvInput
func (_e *MockCustomImageDeploymentRepository_Expecter) UpdateECRDeploymentEnv(ctx interface{}, in interface{}) *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call {
	return &MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call{Call: _e.mock.On("UpdateECRDeploymentEnv", ctx, in)}
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call) Run(run func(ctx context.Context, in repository.UpdateECRDeploymentEnvInput)) *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context), args[1].(repository.UpdateECRDeploymentEnvInput))
	})
	return _c
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call) Return(_a0 error) *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call) RunAndReturn(run func(context.Context, repository.UpdateECRDeploymentEnvInput) error) *MockCustomImageDeploymentRepository_UpdateECRDeploymentEnv_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockCustomImageDeploymentRepository creates a new instance of MockCustomImageDeploymentRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCustomImageDeploymentRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCustomImageDeploymentRepository {
	mock := &MockCustomImageDeploymentRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
