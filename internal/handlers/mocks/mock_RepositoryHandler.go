// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockRepositoryHandler is an autogenerated mock type for the RepositoryHandler type
type MockRepositoryHandler struct {
	mock.Mock
}

type MockRepositoryHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockRepositoryHandler) EXPECT() *MockRepositoryHandler_Expecter {
	return &MockRepositoryHandler_Expecter{mock: &_m.Mock}
}

// AddRepo provides a mock function with given fields: c
func (_m *MockRepositoryHandler) AddRepo(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_AddRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AddRepo'
type MockRepositoryHandler_AddRepo_Call struct {
	*mock.Call
}

// AddRepo is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) AddRepo(c interface{}) *MockRepositoryHandler_AddRepo_Call {
	return &MockRepositoryHandler_AddRepo_Call{Call: _e.mock.On("AddRepo", c)}
}

func (_c *MockRepositoryHandler_AddRepo_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_AddRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_AddRepo_Call) Return() *MockRepositoryHandler_AddRepo_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_AddRepo_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_AddRepo_Call {
	_c.Run(run)
	return _c
}

// ArchiveRepository provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ArchiveRepository(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ArchiveRepository_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ArchiveRepository'
type MockRepositoryHandler_ArchiveRepository_Call struct {
	*mock.Call
}

// ArchiveRepository is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ArchiveRepository(c interface{}) *MockRepositoryHandler_ArchiveRepository_Call {
	return &MockRepositoryHandler_ArchiveRepository_Call{Call: _e.mock.On("ArchiveRepository", c)}
}

func (_c *MockRepositoryHandler_ArchiveRepository_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ArchiveRepository_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ArchiveRepository_Call) Return() *MockRepositoryHandler_ArchiveRepository_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ArchiveRepository_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ArchiveRepository_Call {
	_c.Run(run)
	return _c
}

// CreateEnv provides a mock function with given fields: c
func (_m *MockRepositoryHandler) CreateEnv(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_CreateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateEnv'
type MockRepositoryHandler_CreateEnv_Call struct {
	*mock.Call
}

// CreateEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) CreateEnv(c interface{}) *MockRepositoryHandler_CreateEnv_Call {
	return &MockRepositoryHandler_CreateEnv_Call{Call: _e.mock.On("CreateEnv", c)}
}

func (_c *MockRepositoryHandler_CreateEnv_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_CreateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_CreateEnv_Call) Return() *MockRepositoryHandler_CreateEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_CreateEnv_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_CreateEnv_Call {
	_c.Run(run)
	return _c
}

// CreateRepoAccessToken provides a mock function with given fields: c
func (_m *MockRepositoryHandler) CreateRepoAccessToken(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_CreateRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoAccessToken'
type MockRepositoryHandler_CreateRepoAccessToken_Call struct {
	*mock.Call
}

// CreateRepoAccessToken is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) CreateRepoAccessToken(c interface{}) *MockRepositoryHandler_CreateRepoAccessToken_Call {
	return &MockRepositoryHandler_CreateRepoAccessToken_Call{Call: _e.mock.On("CreateRepoAccessToken", c)}
}

func (_c *MockRepositoryHandler_CreateRepoAccessToken_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_CreateRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoAccessToken_Call) Return() *MockRepositoryHandler_CreateRepoAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_CreateRepoAccessToken_Call {
	_c.Run(run)
	return _c
}

// CreateRepoCommit provides a mock function with given fields: c
func (_m *MockRepositoryHandler) CreateRepoCommit(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_CreateRepoCommit_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoCommit'
type MockRepositoryHandler_CreateRepoCommit_Call struct {
	*mock.Call
}

// CreateRepoCommit is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) CreateRepoCommit(c interface{}) *MockRepositoryHandler_CreateRepoCommit_Call {
	return &MockRepositoryHandler_CreateRepoCommit_Call{Call: _e.mock.On("CreateRepoCommit", c)}
}

func (_c *MockRepositoryHandler_CreateRepoCommit_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_CreateRepoCommit_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoCommit_Call) Return() *MockRepositoryHandler_CreateRepoCommit_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoCommit_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_CreateRepoCommit_Call {
	_c.Run(run)
	return _c
}

// CreateRepoTag provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) CreateRepoTag(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_CreateRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateRepoTag'
type MockRepositoryHandler_CreateRepoTag_Call struct {
	*mock.Call
}

// CreateRepoTag is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) CreateRepoTag(ctx interface{}) *MockRepositoryHandler_CreateRepoTag_Call {
	return &MockRepositoryHandler_CreateRepoTag_Call{Call: _e.mock.On("CreateRepoTag", ctx)}
}

func (_c *MockRepositoryHandler_CreateRepoTag_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_CreateRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoTag_Call) Return() *MockRepositoryHandler_CreateRepoTag_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_CreateRepoTag_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_CreateRepoTag_Call {
	_c.Run(run)
	return _c
}

// DeleteEnv provides a mock function with given fields: c
func (_m *MockRepositoryHandler) DeleteEnv(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_DeleteEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteEnv'
type MockRepositoryHandler_DeleteEnv_Call struct {
	*mock.Call
}

// DeleteEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) DeleteEnv(c interface{}) *MockRepositoryHandler_DeleteEnv_Call {
	return &MockRepositoryHandler_DeleteEnv_Call{Call: _e.mock.On("DeleteEnv", c)}
}

func (_c *MockRepositoryHandler_DeleteEnv_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_DeleteEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_DeleteEnv_Call) Return() *MockRepositoryHandler_DeleteEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_DeleteEnv_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_DeleteEnv_Call {
	_c.Run(run)
	return _c
}

// DeleteRepo provides a mock function with given fields: c
func (_m *MockRepositoryHandler) DeleteRepo(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_DeleteRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepo'
type MockRepositoryHandler_DeleteRepo_Call struct {
	*mock.Call
}

// DeleteRepo is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) DeleteRepo(c interface{}) *MockRepositoryHandler_DeleteRepo_Call {
	return &MockRepositoryHandler_DeleteRepo_Call{Call: _e.mock.On("DeleteRepo", c)}
}

func (_c *MockRepositoryHandler_DeleteRepo_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_DeleteRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepo_Call) Return() *MockRepositoryHandler_DeleteRepo_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepo_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_DeleteRepo_Call {
	_c.Run(run)
	return _c
}

// DeleteRepoAccessToken provides a mock function with given fields: c
func (_m *MockRepositoryHandler) DeleteRepoAccessToken(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_DeleteRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoAccessToken'
type MockRepositoryHandler_DeleteRepoAccessToken_Call struct {
	*mock.Call
}

// DeleteRepoAccessToken is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) DeleteRepoAccessToken(c interface{}) *MockRepositoryHandler_DeleteRepoAccessToken_Call {
	return &MockRepositoryHandler_DeleteRepoAccessToken_Call{Call: _e.mock.On("DeleteRepoAccessToken", c)}
}

func (_c *MockRepositoryHandler_DeleteRepoAccessToken_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_DeleteRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoAccessToken_Call) Return() *MockRepositoryHandler_DeleteRepoAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_DeleteRepoAccessToken_Call {
	_c.Run(run)
	return _c
}

// DeleteRepoAvatar provides a mock function with given fields: c
func (_m *MockRepositoryHandler) DeleteRepoAvatar(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_DeleteRepoAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoAvatar'
type MockRepositoryHandler_DeleteRepoAvatar_Call struct {
	*mock.Call
}

// DeleteRepoAvatar is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) DeleteRepoAvatar(c interface{}) *MockRepositoryHandler_DeleteRepoAvatar_Call {
	return &MockRepositoryHandler_DeleteRepoAvatar_Call{Call: _e.mock.On("DeleteRepoAvatar", c)}
}

func (_c *MockRepositoryHandler_DeleteRepoAvatar_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_DeleteRepoAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoAvatar_Call) Return() *MockRepositoryHandler_DeleteRepoAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_DeleteRepoAvatar_Call {
	_c.Run(run)
	return _c
}

// DeleteRepoTag provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) DeleteRepoTag(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_DeleteRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteRepoTag'
type MockRepositoryHandler_DeleteRepoTag_Call struct {
	*mock.Call
}

// DeleteRepoTag is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) DeleteRepoTag(ctx interface{}) *MockRepositoryHandler_DeleteRepoTag_Call {
	return &MockRepositoryHandler_DeleteRepoTag_Call{Call: _e.mock.On("DeleteRepoTag", ctx)}
}

func (_c *MockRepositoryHandler_DeleteRepoTag_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_DeleteRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoTag_Call) Return() *MockRepositoryHandler_DeleteRepoTag_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_DeleteRepoTag_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_DeleteRepoTag_Call {
	_c.Run(run)
	return _c
}

// GetComposeServices provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetComposeServices(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetComposeServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetComposeServices'
type MockRepositoryHandler_GetComposeServices_Call struct {
	*mock.Call
}

// GetComposeServices is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetComposeServices(c interface{}) *MockRepositoryHandler_GetComposeServices_Call {
	return &MockRepositoryHandler_GetComposeServices_Call{Call: _e.mock.On("GetComposeServices", c)}
}

func (_c *MockRepositoryHandler_GetComposeServices_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetComposeServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetComposeServices_Call) Return() *MockRepositoryHandler_GetComposeServices_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetComposeServices_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetComposeServices_Call {
	_c.Run(run)
	return _c
}

// GetDeploymentBuildLogs provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetDeploymentBuildLogs(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetDeploymentBuildLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentBuildLogs'
type MockRepositoryHandler_GetDeploymentBuildLogs_Call struct {
	*mock.Call
}

// GetDeploymentBuildLogs is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetDeploymentBuildLogs(c interface{}) *MockRepositoryHandler_GetDeploymentBuildLogs_Call {
	return &MockRepositoryHandler_GetDeploymentBuildLogs_Call{Call: _e.mock.On("GetDeploymentBuildLogs", c)}
}

func (_c *MockRepositoryHandler_GetDeploymentBuildLogs_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetDeploymentBuildLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentBuildLogs_Call) Return() *MockRepositoryHandler_GetDeploymentBuildLogs_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentBuildLogs_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetDeploymentBuildLogs_Call {
	_c.Run(run)
	return _c
}

// GetDeploymentPodLogs provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetDeploymentPodLogs(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetDeploymentPodLogs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentPodLogs'
type MockRepositoryHandler_GetDeploymentPodLogs_Call struct {
	*mock.Call
}

// GetDeploymentPodLogs is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetDeploymentPodLogs(c interface{}) *MockRepositoryHandler_GetDeploymentPodLogs_Call {
	return &MockRepositoryHandler_GetDeploymentPodLogs_Call{Call: _e.mock.On("GetDeploymentPodLogs", c)}
}

func (_c *MockRepositoryHandler_GetDeploymentPodLogs_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetDeploymentPodLogs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentPodLogs_Call) Return() *MockRepositoryHandler_GetDeploymentPodLogs_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentPodLogs_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetDeploymentPodLogs_Call {
	_c.Run(run)
	return _c
}

// GetDeploymentStatus provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetDeploymentStatus(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDeploymentStatus'
type MockRepositoryHandler_GetDeploymentStatus_Call struct {
	*mock.Call
}

// GetDeploymentStatus is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetDeploymentStatus(c interface{}) *MockRepositoryHandler_GetDeploymentStatus_Call {
	return &MockRepositoryHandler_GetDeploymentStatus_Call{Call: _e.mock.On("GetDeploymentStatus", c)}
}

func (_c *MockRepositoryHandler_GetDeploymentStatus_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentStatus_Call) Return() *MockRepositoryHandler_GetDeploymentStatus_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetDeploymentStatus_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetDeploymentStatus_Call {
	_c.Run(run)
	return _c
}

// GetEnv provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetEnv(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetEnv'
type MockRepositoryHandler_GetEnv_Call struct {
	*mock.Call
}

// GetEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetEnv(c interface{}) *MockRepositoryHandler_GetEnv_Call {
	return &MockRepositoryHandler_GetEnv_Call{Call: _e.mock.On("GetEnv", c)}
}

func (_c *MockRepositoryHandler_GetEnv_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetEnv_Call) Return() *MockRepositoryHandler_GetEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetEnv_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetEnv_Call {
	_c.Run(run)
	return _c
}

// GetFileContent provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetFileContent(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetFileContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFileContent'
type MockRepositoryHandler_GetFileContent_Call struct {
	*mock.Call
}

// GetFileContent is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetFileContent(c interface{}) *MockRepositoryHandler_GetFileContent_Call {
	return &MockRepositoryHandler_GetFileContent_Call{Call: _e.mock.On("GetFileContent", c)}
}

func (_c *MockRepositoryHandler_GetFileContent_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetFileContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetFileContent_Call) Return() *MockRepositoryHandler_GetFileContent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetFileContent_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetFileContent_Call {
	_c.Run(run)
	return _c
}

// GetHeaderRawFileContent provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) GetHeaderRawFileContent(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_GetHeaderRawFileContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHeaderRawFileContent'
type MockRepositoryHandler_GetHeaderRawFileContent_Call struct {
	*mock.Call
}

// GetHeaderRawFileContent is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetHeaderRawFileContent(ctx interface{}) *MockRepositoryHandler_GetHeaderRawFileContent_Call {
	return &MockRepositoryHandler_GetHeaderRawFileContent_Call{Call: _e.mock.On("GetHeaderRawFileContent", ctx)}
}

func (_c *MockRepositoryHandler_GetHeaderRawFileContent_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_GetHeaderRawFileContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetHeaderRawFileContent_Call) Return() *MockRepositoryHandler_GetHeaderRawFileContent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetHeaderRawFileContent_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetHeaderRawFileContent_Call {
	_c.Run(run)
	return _c
}

// GetRawFileContent provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetRawFileContent(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetRawFileContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRawFileContent'
type MockRepositoryHandler_GetRawFileContent_Call struct {
	*mock.Call
}

// GetRawFileContent is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetRawFileContent(c interface{}) *MockRepositoryHandler_GetRawFileContent_Call {
	return &MockRepositoryHandler_GetRawFileContent_Call{Call: _e.mock.On("GetRawFileContent", c)}
}

func (_c *MockRepositoryHandler_GetRawFileContent_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetRawFileContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetRawFileContent_Call) Return() *MockRepositoryHandler_GetRawFileContent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetRawFileContent_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetRawFileContent_Call {
	_c.Run(run)
	return _c
}

// GetReadmeFileContent provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetReadmeFileContent(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetReadmeFileContent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetReadmeFileContent'
type MockRepositoryHandler_GetReadmeFileContent_Call struct {
	*mock.Call
}

// GetReadmeFileContent is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetReadmeFileContent(c interface{}) *MockRepositoryHandler_GetReadmeFileContent_Call {
	return &MockRepositoryHandler_GetReadmeFileContent_Call{Call: _e.mock.On("GetReadmeFileContent", c)}
}

func (_c *MockRepositoryHandler_GetReadmeFileContent_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetReadmeFileContent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetReadmeFileContent_Call) Return() *MockRepositoryHandler_GetReadmeFileContent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetReadmeFileContent_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetReadmeFileContent_Call {
	_c.Run(run)
	return _c
}

// GetRepoInfo provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetRepoInfo(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetRepoInfo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoInfo'
type MockRepositoryHandler_GetRepoInfo_Call struct {
	*mock.Call
}

// GetRepoInfo is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetRepoInfo(c interface{}) *MockRepositoryHandler_GetRepoInfo_Call {
	return &MockRepositoryHandler_GetRepoInfo_Call{Call: _e.mock.On("GetRepoInfo", c)}
}

func (_c *MockRepositoryHandler_GetRepoInfo_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetRepoInfo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetRepoInfo_Call) Return() *MockRepositoryHandler_GetRepoInfo_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetRepoInfo_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetRepoInfo_Call {
	_c.Run(run)
	return _c
}

// GetRepoTag provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) GetRepoTag(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_GetRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepoTag'
type MockRepositoryHandler_GetRepoTag_Call struct {
	*mock.Call
}

// GetRepoTag is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetRepoTag(ctx interface{}) *MockRepositoryHandler_GetRepoTag_Call {
	return &MockRepositoryHandler_GetRepoTag_Call{Call: _e.mock.On("GetRepoTag", ctx)}
}

func (_c *MockRepositoryHandler_GetRepoTag_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_GetRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetRepoTag_Call) Return() *MockRepositoryHandler_GetRepoTag_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetRepoTag_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetRepoTag_Call {
	_c.Run(run)
	return _c
}

// GetRepositoryMember provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetRepositoryMember(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRepositoryMember'
type MockRepositoryHandler_GetRepositoryMember_Call struct {
	*mock.Call
}

// GetRepositoryMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetRepositoryMember(c interface{}) *MockRepositoryHandler_GetRepositoryMember_Call {
	return &MockRepositoryHandler_GetRepositoryMember_Call{Call: _e.mock.On("GetRepositoryMember", c)}
}

func (_c *MockRepositoryHandler_GetRepositoryMember_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetRepositoryMember_Call) Return() *MockRepositoryHandler_GetRepositoryMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetRepositoryMember_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetRepositoryMember_Call {
	_c.Run(run)
	return _c
}

// GetSingleRepoBranch provides a mock function with given fields: c
func (_m *MockRepositoryHandler) GetSingleRepoBranch(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_GetSingleRepoBranch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSingleRepoBranch'
type MockRepositoryHandler_GetSingleRepoBranch_Call struct {
	*mock.Call
}

// GetSingleRepoBranch is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) GetSingleRepoBranch(c interface{}) *MockRepositoryHandler_GetSingleRepoBranch_Call {
	return &MockRepositoryHandler_GetSingleRepoBranch_Call{Call: _e.mock.On("GetSingleRepoBranch", c)}
}

func (_c *MockRepositoryHandler_GetSingleRepoBranch_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_GetSingleRepoBranch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_GetSingleRepoBranch_Call) Return() *MockRepositoryHandler_GetSingleRepoBranch_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_GetSingleRepoBranch_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_GetSingleRepoBranch_Call {
	_c.Run(run)
	return _c
}

// InviteUser provides a mock function with given fields: c
func (_m *MockRepositoryHandler) InviteUser(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockRepositoryHandler_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) InviteUser(c interface{}) *MockRepositoryHandler_InviteUser_Call {
	return &MockRepositoryHandler_InviteUser_Call{Call: _e.mock.On("InviteUser", c)}
}

func (_c *MockRepositoryHandler_InviteUser_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_InviteUser_Call) Return() *MockRepositoryHandler_InviteUser_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_InviteUser_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_InviteUser_Call {
	_c.Run(run)
	return _c
}

// InviteUsers provides a mock function with given fields: c
func (_m *MockRepositoryHandler) InviteUsers(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_InviteUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUsers'
type MockRepositoryHandler_InviteUsers_Call struct {
	*mock.Call
}

// InviteUsers is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) InviteUsers(c interface{}) *MockRepositoryHandler_InviteUsers_Call {
	return &MockRepositoryHandler_InviteUsers_Call{Call: _e.mock.On("InviteUsers", c)}
}

func (_c *MockRepositoryHandler_InviteUsers_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_InviteUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_InviteUsers_Call) Return() *MockRepositoryHandler_InviteUsers_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_InviteUsers_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_InviteUsers_Call {
	_c.Run(run)
	return _c
}

// ListDeployments provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) ListDeployments(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_ListDeployments_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListDeployments'
type MockRepositoryHandler_ListDeployments_Call struct {
	*mock.Call
}

// ListDeployments is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListDeployments(ctx interface{}) *MockRepositoryHandler_ListDeployments_Call {
	return &MockRepositoryHandler_ListDeployments_Call{Call: _e.mock.On("ListDeployments", ctx)}
}

func (_c *MockRepositoryHandler_ListDeployments_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_ListDeployments_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListDeployments_Call) Return() *MockRepositoryHandler_ListDeployments_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListDeployments_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListDeployments_Call {
	_c.Run(run)
	return _c
}

// ListRepoAccessToken provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoAccessToken(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoAccessToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoAccessToken'
type MockRepositoryHandler_ListRepoAccessToken_Call struct {
	*mock.Call
}

// ListRepoAccessToken is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoAccessToken(c interface{}) *MockRepositoryHandler_ListRepoAccessToken_Call {
	return &MockRepositoryHandler_ListRepoAccessToken_Call{Call: _e.mock.On("ListRepoAccessToken", c)}
}

func (_c *MockRepositoryHandler_ListRepoAccessToken_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoAccessToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoAccessToken_Call) Return() *MockRepositoryHandler_ListRepoAccessToken_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoAccessToken_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoAccessToken_Call {
	_c.Run(run)
	return _c
}

// ListRepoBranches provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoBranches(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoBranches_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoBranches'
type MockRepositoryHandler_ListRepoBranches_Call struct {
	*mock.Call
}

// ListRepoBranches is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoBranches(c interface{}) *MockRepositoryHandler_ListRepoBranches_Call {
	return &MockRepositoryHandler_ListRepoBranches_Call{Call: _e.mock.On("ListRepoBranches", c)}
}

func (_c *MockRepositoryHandler_ListRepoBranches_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoBranches_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoBranches_Call) Return() *MockRepositoryHandler_ListRepoBranches_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoBranches_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoBranches_Call {
	_c.Run(run)
	return _c
}

// ListRepoCommits provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoCommits(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoCommits_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoCommits'
type MockRepositoryHandler_ListRepoCommits_Call struct {
	*mock.Call
}

// ListRepoCommits is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoCommits(c interface{}) *MockRepositoryHandler_ListRepoCommits_Call {
	return &MockRepositoryHandler_ListRepoCommits_Call{Call: _e.mock.On("ListRepoCommits", c)}
}

func (_c *MockRepositoryHandler_ListRepoCommits_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoCommits_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoCommits_Call) Return() *MockRepositoryHandler_ListRepoCommits_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoCommits_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoCommits_Call {
	_c.Run(run)
	return _c
}

// ListRepoContributors provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoContributors(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoContributors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoContributors'
type MockRepositoryHandler_ListRepoContributors_Call struct {
	*mock.Call
}

// ListRepoContributors is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoContributors(c interface{}) *MockRepositoryHandler_ListRepoContributors_Call {
	return &MockRepositoryHandler_ListRepoContributors_Call{Call: _e.mock.On("ListRepoContributors", c)}
}

func (_c *MockRepositoryHandler_ListRepoContributors_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoContributors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoContributors_Call) Return() *MockRepositoryHandler_ListRepoContributors_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoContributors_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoContributors_Call {
	_c.Run(run)
	return _c
}

// ListRepoFiles provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoFiles(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoFiles_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoFiles'
type MockRepositoryHandler_ListRepoFiles_Call struct {
	*mock.Call
}

// ListRepoFiles is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoFiles(c interface{}) *MockRepositoryHandler_ListRepoFiles_Call {
	return &MockRepositoryHandler_ListRepoFiles_Call{Call: _e.mock.On("ListRepoFiles", c)}
}

func (_c *MockRepositoryHandler_ListRepoFiles_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoFiles_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoFiles_Call) Return() *MockRepositoryHandler_ListRepoFiles_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoFiles_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoFiles_Call {
	_c.Run(run)
	return _c
}

// ListRepoMembers provides a mock function with given fields: c
func (_m *MockRepositoryHandler) ListRepoMembers(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_ListRepoMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoMembers'
type MockRepositoryHandler_ListRepoMembers_Call struct {
	*mock.Call
}

// ListRepoMembers is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoMembers(c interface{}) *MockRepositoryHandler_ListRepoMembers_Call {
	return &MockRepositoryHandler_ListRepoMembers_Call{Call: _e.mock.On("ListRepoMembers", c)}
}

func (_c *MockRepositoryHandler_ListRepoMembers_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_ListRepoMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoMembers_Call) Return() *MockRepositoryHandler_ListRepoMembers_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoMembers_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoMembers_Call {
	_c.Run(run)
	return _c
}

// ListRepoTags provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) ListRepoTags(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_ListRepoTags_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTags'
type MockRepositoryHandler_ListRepoTags_Call struct {
	*mock.Call
}

// ListRepoTags is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoTags(ctx interface{}) *MockRepositoryHandler_ListRepoTags_Call {
	return &MockRepositoryHandler_ListRepoTags_Call{Call: _e.mock.On("ListRepoTags", ctx)}
}

func (_c *MockRepositoryHandler_ListRepoTags_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_ListRepoTags_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoTags_Call) Return() *MockRepositoryHandler_ListRepoTags_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoTags_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoTags_Call {
	_c.Run(run)
	return _c
}

// ListRepoTemplates provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) ListRepoTemplates(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_ListRepoTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepoTemplates'
type MockRepositoryHandler_ListRepoTemplates_Call struct {
	*mock.Call
}

// ListRepoTemplates is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepoTemplates(ctx interface{}) *MockRepositoryHandler_ListRepoTemplates_Call {
	return &MockRepositoryHandler_ListRepoTemplates_Call{Call: _e.mock.On("ListRepoTemplates", ctx)}
}

func (_c *MockRepositoryHandler_ListRepoTemplates_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_ListRepoTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepoTemplates_Call) Return() *MockRepositoryHandler_ListRepoTemplates_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepoTemplates_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepoTemplates_Call {
	_c.Run(run)
	return _c
}

// ListRepos provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) ListRepos(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_ListRepos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListRepos'
type MockRepositoryHandler_ListRepos_Call struct {
	*mock.Call
}

// ListRepos is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) ListRepos(ctx interface{}) *MockRepositoryHandler_ListRepos_Call {
	return &MockRepositoryHandler_ListRepos_Call{Call: _e.mock.On("ListRepos", ctx)}
}

func (_c *MockRepositoryHandler_ListRepos_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_ListRepos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_ListRepos_Call) Return() *MockRepositoryHandler_ListRepos_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_ListRepos_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_ListRepos_Call {
	_c.Run(run)
	return _c
}

// PushEventWebhookHandler provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) PushEventWebhookHandler(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_PushEventWebhookHandler_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PushEventWebhookHandler'
type MockRepositoryHandler_PushEventWebhookHandler_Call struct {
	*mock.Call
}

// PushEventWebhookHandler is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) PushEventWebhookHandler(ctx interface{}) *MockRepositoryHandler_PushEventWebhookHandler_Call {
	return &MockRepositoryHandler_PushEventWebhookHandler_Call{Call: _e.mock.On("PushEventWebhookHandler", ctx)}
}

func (_c *MockRepositoryHandler_PushEventWebhookHandler_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_PushEventWebhookHandler_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_PushEventWebhookHandler_Call) Return() *MockRepositoryHandler_PushEventWebhookHandler_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_PushEventWebhookHandler_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_PushEventWebhookHandler_Call {
	_c.Run(run)
	return _c
}

// RemoveRepositoryMember provides a mock function with given fields: c
func (_m *MockRepositoryHandler) RemoveRepositoryMember(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_RemoveRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveRepositoryMember'
type MockRepositoryHandler_RemoveRepositoryMember_Call struct {
	*mock.Call
}

// RemoveRepositoryMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) RemoveRepositoryMember(c interface{}) *MockRepositoryHandler_RemoveRepositoryMember_Call {
	return &MockRepositoryHandler_RemoveRepositoryMember_Call{Call: _e.mock.On("RemoveRepositoryMember", c)}
}

func (_c *MockRepositoryHandler_RemoveRepositoryMember_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_RemoveRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_RemoveRepositoryMember_Call) Return() *MockRepositoryHandler_RemoveRepositoryMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_RemoveRepositoryMember_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_RemoveRepositoryMember_Call {
	_c.Run(run)
	return _c
}

// RestartDeployment provides a mock function with given fields: c
func (_m *MockRepositoryHandler) RestartDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_RestartDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RestartDeployment'
type MockRepositoryHandler_RestartDeployment_Call struct {
	*mock.Call
}

// RestartDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) RestartDeployment(c interface{}) *MockRepositoryHandler_RestartDeployment_Call {
	return &MockRepositoryHandler_RestartDeployment_Call{Call: _e.mock.On("RestartDeployment", c)}
}

func (_c *MockRepositoryHandler_RestartDeployment_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_RestartDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_RestartDeployment_Call) Return() *MockRepositoryHandler_RestartDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_RestartDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_RestartDeployment_Call {
	_c.Run(run)
	return _c
}

// StartDeployment provides a mock function with given fields: c
func (_m *MockRepositoryHandler) StartDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_StartDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartDeployment'
type MockRepositoryHandler_StartDeployment_Call struct {
	*mock.Call
}

// StartDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) StartDeployment(c interface{}) *MockRepositoryHandler_StartDeployment_Call {
	return &MockRepositoryHandler_StartDeployment_Call{Call: _e.mock.On("StartDeployment", c)}
}

func (_c *MockRepositoryHandler_StartDeployment_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_StartDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_StartDeployment_Call) Return() *MockRepositoryHandler_StartDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_StartDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_StartDeployment_Call {
	_c.Run(run)
	return _c
}

// StopDeployment provides a mock function with given fields: c
func (_m *MockRepositoryHandler) StopDeployment(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_StopDeployment_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopDeployment'
type MockRepositoryHandler_StopDeployment_Call struct {
	*mock.Call
}

// StopDeployment is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) StopDeployment(c interface{}) *MockRepositoryHandler_StopDeployment_Call {
	return &MockRepositoryHandler_StopDeployment_Call{Call: _e.mock.On("StopDeployment", c)}
}

func (_c *MockRepositoryHandler_StopDeployment_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_StopDeployment_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_StopDeployment_Call) Return() *MockRepositoryHandler_StopDeployment_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_StopDeployment_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_StopDeployment_Call {
	_c.Run(run)
	return _c
}

// UpdateDeploymentStatus provides a mock function with given fields: c
func (_m *MockRepositoryHandler) UpdateDeploymentStatus(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_UpdateDeploymentStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateDeploymentStatus'
type MockRepositoryHandler_UpdateDeploymentStatus_Call struct {
	*mock.Call
}

// UpdateDeploymentStatus is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) UpdateDeploymentStatus(c interface{}) *MockRepositoryHandler_UpdateDeploymentStatus_Call {
	return &MockRepositoryHandler_UpdateDeploymentStatus_Call{Call: _e.mock.On("UpdateDeploymentStatus", c)}
}

func (_c *MockRepositoryHandler_UpdateDeploymentStatus_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_UpdateDeploymentStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UpdateDeploymentStatus_Call) Return() *MockRepositoryHandler_UpdateDeploymentStatus_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UpdateDeploymentStatus_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UpdateDeploymentStatus_Call {
	_c.Run(run)
	return _c
}

// UpdateEnv provides a mock function with given fields: c
func (_m *MockRepositoryHandler) UpdateEnv(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_UpdateEnv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateEnv'
type MockRepositoryHandler_UpdateEnv_Call struct {
	*mock.Call
}

// UpdateEnv is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) UpdateEnv(c interface{}) *MockRepositoryHandler_UpdateEnv_Call {
	return &MockRepositoryHandler_UpdateEnv_Call{Call: _e.mock.On("UpdateEnv", c)}
}

func (_c *MockRepositoryHandler_UpdateEnv_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_UpdateEnv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UpdateEnv_Call) Return() *MockRepositoryHandler_UpdateEnv_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UpdateEnv_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UpdateEnv_Call {
	_c.Run(run)
	return _c
}

// UpdateRepo provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) UpdateRepo(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_UpdateRepo_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepo'
type MockRepositoryHandler_UpdateRepo_Call struct {
	*mock.Call
}

// UpdateRepo is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) UpdateRepo(ctx interface{}) *MockRepositoryHandler_UpdateRepo_Call {
	return &MockRepositoryHandler_UpdateRepo_Call{Call: _e.mock.On("UpdateRepo", ctx)}
}

func (_c *MockRepositoryHandler_UpdateRepo_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_UpdateRepo_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepo_Call) Return() *MockRepositoryHandler_UpdateRepo_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepo_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UpdateRepo_Call {
	_c.Run(run)
	return _c
}

// UpdateRepoTag provides a mock function with given fields: ctx
func (_m *MockRepositoryHandler) UpdateRepoTag(ctx *gin.Context) {
	_m.Called(ctx)
}

// MockRepositoryHandler_UpdateRepoTag_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepoTag'
type MockRepositoryHandler_UpdateRepoTag_Call struct {
	*mock.Call
}

// UpdateRepoTag is a helper method to define mock.On call
//   - ctx *gin.Context
func (_e *MockRepositoryHandler_Expecter) UpdateRepoTag(ctx interface{}) *MockRepositoryHandler_UpdateRepoTag_Call {
	return &MockRepositoryHandler_UpdateRepoTag_Call{Call: _e.mock.On("UpdateRepoTag", ctx)}
}

func (_c *MockRepositoryHandler_UpdateRepoTag_Call) Run(run func(ctx *gin.Context)) *MockRepositoryHandler_UpdateRepoTag_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepoTag_Call) Return() *MockRepositoryHandler_UpdateRepoTag_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepoTag_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UpdateRepoTag_Call {
	_c.Run(run)
	return _c
}

// UpdateRepositoryMember provides a mock function with given fields: c
func (_m *MockRepositoryHandler) UpdateRepositoryMember(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_UpdateRepositoryMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateRepositoryMember'
type MockRepositoryHandler_UpdateRepositoryMember_Call struct {
	*mock.Call
}

// UpdateRepositoryMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) UpdateRepositoryMember(c interface{}) *MockRepositoryHandler_UpdateRepositoryMember_Call {
	return &MockRepositoryHandler_UpdateRepositoryMember_Call{Call: _e.mock.On("UpdateRepositoryMember", c)}
}

func (_c *MockRepositoryHandler_UpdateRepositoryMember_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_UpdateRepositoryMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepositoryMember_Call) Return() *MockRepositoryHandler_UpdateRepositoryMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UpdateRepositoryMember_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UpdateRepositoryMember_Call {
	_c.Run(run)
	return _c
}

// UploadRepoAvatar provides a mock function with given fields: c
func (_m *MockRepositoryHandler) UploadRepoAvatar(c *gin.Context) {
	_m.Called(c)
}

// MockRepositoryHandler_UploadRepoAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadRepoAvatar'
type MockRepositoryHandler_UploadRepoAvatar_Call struct {
	*mock.Call
}

// UploadRepoAvatar is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockRepositoryHandler_Expecter) UploadRepoAvatar(c interface{}) *MockRepositoryHandler_UploadRepoAvatar_Call {
	return &MockRepositoryHandler_UploadRepoAvatar_Call{Call: _e.mock.On("UploadRepoAvatar", c)}
}

func (_c *MockRepositoryHandler_UploadRepoAvatar_Call) Run(run func(c *gin.Context)) *MockRepositoryHandler_UploadRepoAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockRepositoryHandler_UploadRepoAvatar_Call) Return() *MockRepositoryHandler_UploadRepoAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockRepositoryHandler_UploadRepoAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockRepositoryHandler_UploadRepoAvatar_Call {
	_c.Run(run)
	return _c
}

// NewMockRepositoryHandler creates a new instance of MockRepositoryHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockRepositoryHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockRepositoryHandler {
	mock := &MockRepositoryHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
