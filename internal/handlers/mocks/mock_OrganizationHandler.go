// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	gin "github.com/gin-gonic/gin"

	mock "github.com/stretchr/testify/mock"
)

// MockOrganizationHandler is an autogenerated mock type for the OrganizationHandler type
type MockOrganizationHandler struct {
	mock.Mock
}

type MockOrganizationHandler_Expecter struct {
	mock *mock.Mock
}

func (_m *MockOrganizationHandler) EXPECT() *MockOrganizationHandler_Expecter {
	return &MockOrganizationHandler_Expecter{mock: &_m.Mock}
}

// CreateOrganization provides a mock function with given fields: c
func (_m *MockOrganizationHandler) CreateOrganization(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_CreateOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateOrganization'
type MockOrganizationHandler_CreateOrganization_Call struct {
	*mock.Call
}

// CreateOrganization is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) CreateOrganization(c interface{}) *MockOrganizationHandler_CreateOrganization_Call {
	return &MockOrganizationHandler_CreateOrganization_Call{Call: _e.mock.On("CreateOrganization", c)}
}

func (_c *MockOrganizationHandler_CreateOrganization_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_CreateOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_CreateOrganization_Call) Return() *MockOrganizationHandler_CreateOrganization_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_CreateOrganization_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_CreateOrganization_Call {
	_c.Run(run)
	return _c
}

// DeleteOrganization provides a mock function with given fields: c
func (_m *MockOrganizationHandler) DeleteOrganization(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_DeleteOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrganization'
type MockOrganizationHandler_DeleteOrganization_Call struct {
	*mock.Call
}

// DeleteOrganization is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) DeleteOrganization(c interface{}) *MockOrganizationHandler_DeleteOrganization_Call {
	return &MockOrganizationHandler_DeleteOrganization_Call{Call: _e.mock.On("DeleteOrganization", c)}
}

func (_c *MockOrganizationHandler_DeleteOrganization_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_DeleteOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_DeleteOrganization_Call) Return() *MockOrganizationHandler_DeleteOrganization_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_DeleteOrganization_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_DeleteOrganization_Call {
	_c.Run(run)
	return _c
}

// DeleteOrganizationAvatar provides a mock function with given fields: c
func (_m *MockOrganizationHandler) DeleteOrganizationAvatar(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_DeleteOrganizationAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteOrganizationAvatar'
type MockOrganizationHandler_DeleteOrganizationAvatar_Call struct {
	*mock.Call
}

// DeleteOrganizationAvatar is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) DeleteOrganizationAvatar(c interface{}) *MockOrganizationHandler_DeleteOrganizationAvatar_Call {
	return &MockOrganizationHandler_DeleteOrganizationAvatar_Call{Call: _e.mock.On("DeleteOrganizationAvatar", c)}
}

func (_c *MockOrganizationHandler_DeleteOrganizationAvatar_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_DeleteOrganizationAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_DeleteOrganizationAvatar_Call) Return() *MockOrganizationHandler_DeleteOrganizationAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_DeleteOrganizationAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_DeleteOrganizationAvatar_Call {
	_c.Run(run)
	return _c
}

// GetMember provides a mock function with given fields: c
func (_m *MockOrganizationHandler) GetMember(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_GetMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetMember'
type MockOrganizationHandler_GetMember_Call struct {
	*mock.Call
}

// GetMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) GetMember(c interface{}) *MockOrganizationHandler_GetMember_Call {
	return &MockOrganizationHandler_GetMember_Call{Call: _e.mock.On("GetMember", c)}
}

func (_c *MockOrganizationHandler_GetMember_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_GetMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_GetMember_Call) Return() *MockOrganizationHandler_GetMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_GetMember_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_GetMember_Call {
	_c.Run(run)
	return _c
}

// GetOrganization provides a mock function with given fields: c
func (_m *MockOrganizationHandler) GetOrganization(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_GetOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOrganization'
type MockOrganizationHandler_GetOrganization_Call struct {
	*mock.Call
}

// GetOrganization is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) GetOrganization(c interface{}) *MockOrganizationHandler_GetOrganization_Call {
	return &MockOrganizationHandler_GetOrganization_Call{Call: _e.mock.On("GetOrganization", c)}
}

func (_c *MockOrganizationHandler_GetOrganization_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_GetOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_GetOrganization_Call) Return() *MockOrganizationHandler_GetOrganization_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_GetOrganization_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_GetOrganization_Call {
	_c.Run(run)
	return _c
}

// InviteUser provides a mock function with given fields: c
func (_m *MockOrganizationHandler) InviteUser(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_InviteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUser'
type MockOrganizationHandler_InviteUser_Call struct {
	*mock.Call
}

// InviteUser is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) InviteUser(c interface{}) *MockOrganizationHandler_InviteUser_Call {
	return &MockOrganizationHandler_InviteUser_Call{Call: _e.mock.On("InviteUser", c)}
}

func (_c *MockOrganizationHandler_InviteUser_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_InviteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_InviteUser_Call) Return() *MockOrganizationHandler_InviteUser_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_InviteUser_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_InviteUser_Call {
	_c.Run(run)
	return _c
}

// InviteUsers provides a mock function with given fields: c
func (_m *MockOrganizationHandler) InviteUsers(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_InviteUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InviteUsers'
type MockOrganizationHandler_InviteUsers_Call struct {
	*mock.Call
}

// InviteUsers is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) InviteUsers(c interface{}) *MockOrganizationHandler_InviteUsers_Call {
	return &MockOrganizationHandler_InviteUsers_Call{Call: _e.mock.On("InviteUsers", c)}
}

func (_c *MockOrganizationHandler_InviteUsers_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_InviteUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_InviteUsers_Call) Return() *MockOrganizationHandler_InviteUsers_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_InviteUsers_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_InviteUsers_Call {
	_c.Run(run)
	return _c
}

// ListAllOrganizations provides a mock function with given fields: c
func (_m *MockOrganizationHandler) ListAllOrganizations(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_ListAllOrganizations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListAllOrganizations'
type MockOrganizationHandler_ListAllOrganizations_Call struct {
	*mock.Call
}

// ListAllOrganizations is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) ListAllOrganizations(c interface{}) *MockOrganizationHandler_ListAllOrganizations_Call {
	return &MockOrganizationHandler_ListAllOrganizations_Call{Call: _e.mock.On("ListAllOrganizations", c)}
}

func (_c *MockOrganizationHandler_ListAllOrganizations_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_ListAllOrganizations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_ListAllOrganizations_Call) Return() *MockOrganizationHandler_ListAllOrganizations_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_ListAllOrganizations_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_ListAllOrganizations_Call {
	_c.Run(run)
	return _c
}

// ListCurrentUserOrganizations provides a mock function with given fields: c
func (_m *MockOrganizationHandler) ListCurrentUserOrganizations(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_ListCurrentUserOrganizations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListCurrentUserOrganizations'
type MockOrganizationHandler_ListCurrentUserOrganizations_Call struct {
	*mock.Call
}

// ListCurrentUserOrganizations is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) ListCurrentUserOrganizations(c interface{}) *MockOrganizationHandler_ListCurrentUserOrganizations_Call {
	return &MockOrganizationHandler_ListCurrentUserOrganizations_Call{Call: _e.mock.On("ListCurrentUserOrganizations", c)}
}

func (_c *MockOrganizationHandler_ListCurrentUserOrganizations_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_ListCurrentUserOrganizations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_ListCurrentUserOrganizations_Call) Return() *MockOrganizationHandler_ListCurrentUserOrganizations_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_ListCurrentUserOrganizations_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_ListCurrentUserOrganizations_Call {
	_c.Run(run)
	return _c
}

// ListOrganizationMembers provides a mock function with given fields: c
func (_m *MockOrganizationHandler) ListOrganizationMembers(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_ListOrganizationMembers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ListOrganizationMembers'
type MockOrganizationHandler_ListOrganizationMembers_Call struct {
	*mock.Call
}

// ListOrganizationMembers is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) ListOrganizationMembers(c interface{}) *MockOrganizationHandler_ListOrganizationMembers_Call {
	return &MockOrganizationHandler_ListOrganizationMembers_Call{Call: _e.mock.On("ListOrganizationMembers", c)}
}

func (_c *MockOrganizationHandler_ListOrganizationMembers_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_ListOrganizationMembers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_ListOrganizationMembers_Call) Return() *MockOrganizationHandler_ListOrganizationMembers_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_ListOrganizationMembers_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_ListOrganizationMembers_Call {
	_c.Run(run)
	return _c
}

// RemoveMember provides a mock function with given fields: c
func (_m *MockOrganizationHandler) RemoveMember(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_RemoveMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RemoveMember'
type MockOrganizationHandler_RemoveMember_Call struct {
	*mock.Call
}

// RemoveMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) RemoveMember(c interface{}) *MockOrganizationHandler_RemoveMember_Call {
	return &MockOrganizationHandler_RemoveMember_Call{Call: _e.mock.On("RemoveMember", c)}
}

func (_c *MockOrganizationHandler_RemoveMember_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_RemoveMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_RemoveMember_Call) Return() *MockOrganizationHandler_RemoveMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_RemoveMember_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_RemoveMember_Call {
	_c.Run(run)
	return _c
}

// UpdateMember provides a mock function with given fields: c
func (_m *MockOrganizationHandler) UpdateMember(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_UpdateMember_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateMember'
type MockOrganizationHandler_UpdateMember_Call struct {
	*mock.Call
}

// UpdateMember is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) UpdateMember(c interface{}) *MockOrganizationHandler_UpdateMember_Call {
	return &MockOrganizationHandler_UpdateMember_Call{Call: _e.mock.On("UpdateMember", c)}
}

func (_c *MockOrganizationHandler_UpdateMember_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_UpdateMember_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_UpdateMember_Call) Return() *MockOrganizationHandler_UpdateMember_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_UpdateMember_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_UpdateMember_Call {
	_c.Run(run)
	return _c
}

// UpdateOrganization provides a mock function with given fields: c
func (_m *MockOrganizationHandler) UpdateOrganization(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_UpdateOrganization_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateOrganization'
type MockOrganizationHandler_UpdateOrganization_Call struct {
	*mock.Call
}

// UpdateOrganization is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) UpdateOrganization(c interface{}) *MockOrganizationHandler_UpdateOrganization_Call {
	return &MockOrganizationHandler_UpdateOrganization_Call{Call: _e.mock.On("UpdateOrganization", c)}
}

func (_c *MockOrganizationHandler_UpdateOrganization_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_UpdateOrganization_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_UpdateOrganization_Call) Return() *MockOrganizationHandler_UpdateOrganization_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_UpdateOrganization_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_UpdateOrganization_Call {
	_c.Run(run)
	return _c
}

// UploadOrganizationAvatar provides a mock function with given fields: c
func (_m *MockOrganizationHandler) UploadOrganizationAvatar(c *gin.Context) {
	_m.Called(c)
}

// MockOrganizationHandler_UploadOrganizationAvatar_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UploadOrganizationAvatar'
type MockOrganizationHandler_UploadOrganizationAvatar_Call struct {
	*mock.Call
}

// UploadOrganizationAvatar is a helper method to define mock.On call
//   - c *gin.Context
func (_e *MockOrganizationHandler_Expecter) UploadOrganizationAvatar(c interface{}) *MockOrganizationHandler_UploadOrganizationAvatar_Call {
	return &MockOrganizationHandler_UploadOrganizationAvatar_Call{Call: _e.mock.On("UploadOrganizationAvatar", c)}
}

func (_c *MockOrganizationHandler_UploadOrganizationAvatar_Call) Run(run func(c *gin.Context)) *MockOrganizationHandler_UploadOrganizationAvatar_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*gin.Context))
	})
	return _c
}

func (_c *MockOrganizationHandler_UploadOrganizationAvatar_Call) Return() *MockOrganizationHandler_UploadOrganizationAvatar_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockOrganizationHandler_UploadOrganizationAvatar_Call) RunAndReturn(run func(*gin.Context)) *MockOrganizationHandler_UploadOrganizationAvatar_Call {
	_c.Run(run)
	return _c
}

// NewMockOrganizationHandler creates a new instance of MockOrganizationHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockOrganizationHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockOrganizationHandler {
	mock := &MockOrganizationHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
