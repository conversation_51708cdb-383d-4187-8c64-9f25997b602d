package handlers

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/sync/errgroup"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/gateways/gitlab"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/kompose"
	"api-server/internal/usecase/repository"
	"api-server/pkg/oteltrace"
	"api-server/pkg/otelzap"
	"api-server/pkg/validator"
)

type RepositoryHandler interface {
	AddRepo(c *gin.Context)
	ListRepos(ctx *gin.Context)
	UpdateRepo(ctx *gin.Context)
	DeleteRepo(c *gin.Context)
	ListRepoFiles(c *gin.Context)
	GetFileContent(c *gin.Context)
	GetReadmeFileContent(c *gin.Context)
	GetRawFileContent(c *gin.Context)
	GetHeaderRawFileContent(ctx *gin.Context)
	ListRepoBranches(c *gin.Context)
	GetSingleRepoBranch(c *gin.Context)
	ListRepoCommits(c *gin.Context)
	GetRepoInfo(c *gin.Context)
	ArchiveRepository(c *gin.Context)
	ListRepoContributors(c *gin.Context)
	ListRepoMembers(c *gin.Context)
	CreateRepoCommit(c *gin.Context)
	InviteUser(c *gin.Context)
	InviteUsers(c *gin.Context)
	GetRepositoryMember(c *gin.Context)
	RemoveRepositoryMember(c *gin.Context)
	UpdateRepositoryMember(c *gin.Context)

	ListRepoTags(ctx *gin.Context)
	CreateRepoTag(ctx *gin.Context)
	UpdateRepoTag(ctx *gin.Context)
	GetRepoTag(ctx *gin.Context)
	DeleteRepoTag(ctx *gin.Context)

	PushEventWebhookHandler(ctx *gin.Context)
	ListRepoTemplates(ctx *gin.Context)

	GetEnv(c *gin.Context)
	CreateEnv(c *gin.Context)
	UpdateEnv(c *gin.Context)
	DeleteEnv(c *gin.Context)
	ListDeployments(ctx *gin.Context)
	UpdateDeploymentStatus(c *gin.Context)
	StartDeployment(c *gin.Context)
	RestartDeployment(c *gin.Context)
	StopDeployment(c *gin.Context)
	GetDeploymentBuildLogs(c *gin.Context)
	GetDeploymentPodLogs(c *gin.Context)
	GetDeploymentStatus(c *gin.Context)

	UploadRepoAvatar(c *gin.Context)
	DeleteRepoAvatar(c *gin.Context)

	CreateRepoAccessToken(c *gin.Context)
	ListRepoAccessToken(c *gin.Context)
	DeleteRepoAccessToken(c *gin.Context)

	GetComposeServices(c *gin.Context)
}

type repositoryHandlerImpl struct {
	config         *configs.GlobalConfig
	repoUseCase    repository.RepoUsecase
	composeUseCase kompose.KomposeUsecase
}

// NewRepositoryHandler creates a new instance of RepositoryHandler.
// It initializes the handler with the global configuration and repository use case.
//
// Parameters:
//   - config: The global configuration.
//   - repoUseCase: The repository use case.
//
// Returns:
//   - RepositoryHandler: A new RepositoryHandler instance.
func NewRepositoryHandler(config *configs.GlobalConfig, repoUseCase repository.RepoUsecase, composeUseCase kompose.KomposeUsecase) RepositoryHandler {
	return &repositoryHandlerImpl{
		repoUseCase:    repoUseCase,
		config:         config,
		composeUseCase: composeUseCase,
	}
}

// AddRepo godoc
//
//	@Summary	Add new repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		model	body		dto.RepositoryCreateInput		true	"Add repository"
//	@Success	201		{object}	dto.RepositoryCreateResponse	"Return repository information"
//	@Failure	400		{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404		{object}	dto.HTTPError					"Not Found"
//	@Failure	500		{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) AddRepo(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(ParseContext(c), "handlers.repository.AddRepo")
	defer span.End()

	span.AddEvent("get current user id")
	currentUserId, err := GetCurrentUserId(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
		return
	}

	var input dto.RepositoryCreateInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.CurrentUserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if input.Type == enums.RepoType_Spaces && input.Hardware.NodeName == "" {
		err := errors.New("hardware is required for Space repository")
		span.SetStatus(codes.Error, "hardware id is required for space repo")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	span.AddEvent("create repository")
	repoInfo, err := h.repoUseCase.CreateRepository(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryExist) {
			dto.ErrorResponse(c, dto.NewBadRequestError(err))
			return
		}

		if errors.Is(err, usecase.ErrOrganizationNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository created successfully")
	span.SetStatus(codes.Ok, "repository created successfully")
	c.JSON(http.StatusCreated, repoInfo)
}

// ListRepos godoc
//
//	@Summary	List and filter repositories
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		keyword			query		string			false	"search by repos name , user 's name , org name;"
//	@Param		repository_type	query		string			false	"filter by repository type"	Enums(spaces, models, datasets, composes)
//	@Param		tags			query		string			false	"filter by tags; multi-values split by comma"
//	@Param		page			query		int				false	"pagination page number"	minimum(1)
//	@Param		per_page		query		int				false	"number of results per page"
//	@Param		order_by		query		string			false	"order the result by field name"			Enums(created_at, updated_at)
//	@Param		sort			query		string			false	"Sort result by ascending or descending"	Enums(asc, desc)
//	@Success	200				{array}		dto.Repository	"List of matched repository results"
//	@Failure	400				{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404				{object}	dto.HTTPError	"Not Found"
//	@Failure	500				{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepos(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ParseContext(ctx), "handlers.repository.ListRepos")
	defer span.End()

	// span.AddEvent("get current user id")
	// _, err := GetCurrentUserId(c)
	// if err != nil {
	// 	span.SetStatus(codes.Error, "failed to get current user id")
	// 	span.RecordError(err)
	// 	dto.ErrorResponse(ctx, dto.NewForbiddenError(err))
	// 	return
	// }

	var input dto.GetRepositoriesInput
	if err := ctx.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	span.AddEvent("get all repositories")
	output, err := h.repoUseCase.GetAllRepositories(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get all repositories")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repositories listed successfully")
	span.SetStatus(codes.Ok, "repositories listed successfully")
	ctx.JSON(http.StatusOK, output)
}

// DeleteRepo godoc
//
//	@Summary	Delete a repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string	true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string	true	"Repository namespace"
//	@Param		repo_name	path		string	true	"Repository name"
//	@Success	200			string		"Return repository information"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name} [delete]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) DeleteRepo(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.DeleteRepo")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input := dto.DeleteRepositoryInput{
		RepoID:        repoID,
		CurrentUserId: currentUserId,
	}
	if err := h.repoUseCase.DeleteRepository(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to delete repository")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository deleted successfully")
	span.SetStatus(codes.Ok, "repository deleted successfully")
	c.JSON(http.StatusOK, dto.NewHTTPResponse[dto.Repository]().WithMessage("Repository deleted successfully"))
}

// UpdateRepo godoc
//
//	@Summary	Update a repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name} [patch]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) UpdateRepo(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.UpdateRepo")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	var input dto.UpdateRepositoryInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.RepoID = repoID
	input.CurrentUserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := h.repoUseCase.UpdateRepository(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to update repository")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository updated successfully")
	span.SetStatus(codes.Ok, "repository updated successfully")
	c.JSON(http.StatusOK, dto.NewHTTPResponse[dto.Repository]().WithMessage("Updated repository successfully"))
}

// ListRepoFiles godoc
//
//	@Summary	List repository files
//	@Tags		Repository File
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string				true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string				true	"Repository namespace"
//	@Param		repo_name	path		string				true	"Repository name"
//	@Param		ref			query		string				false	"Branch name"
//	@Param		path		query		string				false	"File path"
//	@Param		recursive	query		bool				false	"Used to get a recursive tree. Default is false"
//	@Param		all			query		bool				false	"Used to get all results without pagination. Default is false"
//	@Param		page_token	query		string				false	"Used to get next page of results. Get from X-Next-Page-Token header"
//	@Param		per_page	query		int					false	"Number of result per page. Default is 20. Max is 100"
//	@Success	200			{array}		dto.RepositoryFile	"Ok"
//	@Header		200			{string}	X-Next-Page-Token	"Token to include when requesting the next page of results if there are more results"
//	@Failure	400			{object}	dto.HTTPError		"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError		"Not Found"
//	@Failure	500			{object}	dto.HTTPError		"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/files [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoFiles(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ListRepoFiles")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.GetRepositoryFilesInput
	if err := c.ShouldBind(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	fileDirs, nextPage, err := h.repoUseCase.ListRepoFiles(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository files")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	if !input.Short {
		g, gCtx := errgroup.WithContext(ctx)
		g.SetLimit(8)

		for i := range len(fileDirs) {
			select {
			case <-gCtx.Done():
				break
			default:
				g.Go(func() error {
					// Fetch the last commit for the current file
					lastCommit, err := h.repoUseCase.GetLastRepoCommit(c, dto.GetRepositoryCommitsInput{
						RepoID:        repoID,
						CurrentUserId: currentUserId,
						Ref:           input.Ref,
						Path:          fileDirs[i].Path,
					})
					if err != nil && !errors.Is(err, usecase.ErrCannotFindLastCommit) {
						span.AddEvent("failed to get last commit for file", trace.WithAttributes(
							attribute.String("file_path", fileDirs[i].Path),
							attribute.String("error", err.Error()),
						))
						return err
					}

					if lastCommit == nil {
						return nil
					} else {
						fileDirs[i].LastCommit = lastCommit
					}

					return nil
				})
			}
		}

		if err := g.Wait(); err != nil {
			span.SetStatus(codes.Error, "failed to get last commit for files")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewInternalError(err))
			return
		}
	}

	// set X-Next-Page-Token header if there is a next page to get more results
	if nextPage != nil {
		nextPageToken, err := parsePageToken(*nextPage)
		if err != nil {
			span.SetStatus(codes.Error, "failed to parse page token")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewInternalError(err))
			return
		}
		c.Header("X-Next-Page-Token", nextPageToken)
	}

	c.Header("Access-Control-Expose-Headers", "*")

	span.AddEvent("repository files listed successfully")
	span.SetStatus(codes.Ok, "repository files listed successfully")
	c.JSON(http.StatusOK, fileDirs)
}

// parsePageToken is a helper function to parse the page token from the URL
func parsePageToken(nextPageURL string) (string, error) {
	parsedURL, err := url.Parse(nextPageURL)
	if err != nil {
		return "", errors.New("failed to parse next page URL")
	}

	queryParams := parsedURL.Query()

	return queryParams.Get("page_token"), nil
}

// GetFileContent godoc
//
//	@Summary	Get file contents
//	@Tags		Repository File
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		file_path	path		string						true	"File path"
//	@Param		ref			query		string						false	"Branch name"
//	@Success	200			{array}		dto.RepositoryFileContent	"Ok"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/files/{file_path} [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetFileContent(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetFileContent")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.GetFileFromRepositoryInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	input.Path = url.PathEscape(strings.TrimPrefix(c.Param("file_path"), "/"))

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.GetFileFromRepository(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get file content from repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err, http.StatusText(http.StatusInternalServerError)))
		return
	}

	span.AddEvent("file content retrieved successfully")
	span.SetStatus(codes.Ok, "file content retrieved successfully")
	c.JSON(http.StatusOK, output)
}

// GetReadmeFileContent godoc
//
//	@Summary	Get README file content
//	@Tags		Repository File
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		file_path	path		string						true	"File path"
//	@Param		ref			query		string						false	"Branch name"
//	@Success	200			{array}		dto.RepositoryFileContent	"Ok"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/readme [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetReadmeFileContent(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetReadmeFileContent")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.GetFileFromRepositoryInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	input.Path = "README.md"

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.GetFileFromRepository(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get file content from repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err, http.StatusText(http.StatusInternalServerError)))
		return
	}

	span.AddEvent("file content retrieved successfully")
	span.SetStatus(codes.Ok, "file content retrieved successfully")
	c.JSON(http.StatusOK, output)
}

// GetRawFileContent godoc
//
//	@Summary	Get raw file contents
//	@Tags		Repository File
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		file_path	path		string			true	"File path"
//	@Param		ref			query		string			false	"Branch name"
//	@Success	200			{string}	string			"Raw file content"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/files/raw/{file_path} [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetRawFileContent(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetRawFileContent")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		_, err = GetOrgId(c)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get org id")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewForbiddenError(err))
			return
		}
	}

	var input dto.GetFileFromRepositoryInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	input.Path = url.PathEscape(strings.TrimPrefix(c.Param("file_path"), "/"))

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	gitlabResp, err := h.repoUseCase.GetRawFileFromRepository(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get raw file from repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	for key, value := range gitlabResp.Header {
		if len(value) > 0 {
			c.Header(key, value[0])
		}
	}

	// Forward the body from GitLab response to Gin response
	c.DataFromReader(gitlabResp.StatusCode, gitlabResp.ContentLength, gitlabResp.Header.Get("content-type"), gitlabResp.Body, nil)

	span.AddEvent("raw file content retrieved successfully")
	span.SetStatus(codes.Ok, "raw file content retrieved successfully")
}

// GetHeaderRawFileContent godoc
//
//	@Summary	Get header raw file contents
//	@Tags		Repository File
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		file_path	path		string			true	"File path"
//	@Param		ref			query		string			false	"Branch name"
//	@Success	200			{string}	string			"Raw file content"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/files/raw/{file_path} [head]
//
//	@Security	Bearer
//
// nolint
func (h *repositoryHandlerImpl) GetHeaderRawFileContent(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ctx.Request.Context(), "handlers.GetHeaderRawFileContent")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(ctx))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewForbiddenError(err, http.StatusText(http.StatusForbidden)))
		return
	}

	var input dto.GetFileFromRepositoryInput
	if err := ctx.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(ctx)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	input.Path = url.PathEscape(strings.TrimPrefix(ctx.Param("file_path"), "/"))

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	gitlabResp, err := h.repoUseCase.GetHeaderRawFileFromRepository(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get header raw file from repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(ctx, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(ctx, dto.NewNotFoundError(err))
			return
		}

		if gitlabErr, ok := err.(*gitlab.GitlabError); ok {
			ctx.AbortWithStatusJSON(gitlabErr.StatusCode, dto.NewHTTPError(gitlabErr.StatusCode, gitlabErr))
			return
		}

		dto.ErrorResponse(ctx, dto.NewInternalError(err))
		return
	}

	for key, value := range gitlabResp.Header {
		if len(value) > 0 {
			ctx.Header(key, value[0])
		}
	}

	ctx.Header("Access-Control-Expose-Headers", "*")

	// Forward the body from GitLab response to Gin response
	ctx.DataFromReader(gitlabResp.StatusCode, gitlabResp.ContentLength, gitlabResp.Header.Get("content-type"), gitlabResp.Body, nil)

	span.AddEvent("get header raw file content retrieved successfully")
	span.SetStatus(codes.Ok, "get header raw file content retrieved successfully")
}

// ListRepoBranches godoc
//
//	@Summary	Get repository branches
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Success	200			{array}		dto.RepositoryBranchInfo	"Ok"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/branches [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoBranches(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ListRepoBranches")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input := dto.GetRepositoryBranchesInput{
		RepoID:        repoID,
		CurrentUserId: currentUserId,
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.ListRepoBranches(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository branches")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository branches listed successfully")
	span.SetStatus(codes.Ok, "repository branches listed successfully")
	c.JSON(http.StatusOK, output)
}

// GetSingleRepoBranch godoc
//
//	@Summary	Get single repository branch
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Success	200			{object}	dto.RepositoryBranchInfo	"Ok"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/branch/{branch} [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetSingleRepoBranch(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetSingleRepoBranch")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		_, err = GetOrgId(c)
		if err != nil {
			span.SetStatus(codes.Error, "failed to get org id")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewForbiddenError(err))
			return
		}
	}

	var input dto.GetSingleRepositoryBranchInput
	if err := c.ShouldBindUri(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind uri parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.GetSingleRepoBranch(ctx, input)
	if err != nil {
		if errors.Is(err, usecase.ErrBranchNotFound) {
			span.SetStatus(codes.Error, "branch not found")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}
		span.SetStatus(codes.Error, "failed to get single repository branch")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("single repository branch retrieved successfully")
	span.SetStatus(codes.Ok, "single repository branch retrieved successfully")
	c.JSON(http.StatusOK, output)
}

// ListRepoCommits godoc
//
//	@Summary	Get repository commits
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string					true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string					true	"Repository namespace"
//	@Param		repo_name	path		string					true	"Repository name"
//	@Param		path		query		string					false	"File path"
//	@Param		ref			query		string					false	"Branch name"
//	@Success	200			{array}		dto.RepositoryCommit	"Ok"
//	@Failure	400			{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError			"Not Found"
//	@Failure	500			{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/commits [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoCommits(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ListRepoCommits")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.GetRepositoryCommitsInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.ListRepoCommits(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository commits")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository commits listed successfully")
	span.SetStatus(codes.Ok, "repository commits listed successfully")
	c.JSON(http.StatusOK, output)
}

// GetRepoInfo godoc
//
//	@Summary	Get repo information
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string					true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string					true	"Repository namespace"
//	@Param		repo_name	path		string					true	"Repository name"
//	@Success	200			{array}		dto.GetRepositoryOutput	"Ok"
//	@Failure	400			{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError			"Not Found"
//	@Failure	500			{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name} [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetRepoInfo(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetRepoInfo")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input := dto.GetRepositoryInput{
		RepoID:        repoID,
		CurrentUserId: currentUserId,
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.GetRepoInfo(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository info")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository info retrieved successfully")
	span.SetStatus(codes.Ok, "repository info retrieved successfully")
	c.JSON(http.StatusOK, output)
}

// ListRepoContributors godoc
//
//	@Summary	Get repository contributors
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		ref			query		string						false	"Branch name"
//	@Success	200			{array}		dto.RepositoryContributor	"Ok"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/contributors [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoContributors(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ListRepoContributors")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.GetRepositoryContributorsInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.ListRepoContributors(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository contributors")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository contributors listed successfully")
	span.SetStatus(codes.Ok, "repository contributors listed successfully")
	c.JSON(http.StatusOK, output)
}

// ListRepoMembers godoc
//
//	@Summary	Get repository members
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Success	200			{array}		dto.ListRepositoryMembersOutput	"Ok"
//	@Failure	400			{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError					"Not Found"
//	@Failure	500			{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/members [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoMembers(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ListRepoMembers")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.ListRepositoryMembersInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.CurrentUserId = currentUserId
	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.ListRepoMembers(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository members")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository members listed successfully")
	span.SetStatus(codes.Ok, "repository members listed successfully")
	c.JSON(http.StatusOK, output)
}

// CreateRepoCommit godoc
//
//	@Summary	Create repository commit
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Param		path		query		string							false	"File path"
//	@Param		ref			query		string							false	"Branch name"
//	@Param		model		body		dto.CreateRepositoryCommitInput	true	"Create commit to repository"
//	@Success	200			{array}		dto.RepositoryCommit			"Ok"
//	@Failure	400			{object}	dto.HTTPError					"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError					"Not Found"
//	@Failure	500			{object}	dto.HTTPError					"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/commits [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) CreateRepoCommit(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.CreateRepoCommit")
	defer span.End()

	var input dto.CreateRepositoryCommitInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.RepoID = repoID
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.CreateRepoCommit(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create repository commit")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("repository commit created successfully")
	span.SetStatus(codes.Ok, "repository commit created successfully")
	c.JSON(http.StatusOK, output)
}

// InviteUser godoc
//
//	@Summary	Invite user to repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		model		body		dto.InviteRepoMemberInput	true	"Invite User to Repository information"
//	@Success	200			{object}	string						"Return message invite organization"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/invite [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) InviteUser(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.InviteUser")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	var input dto.InviteRepoMemberInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.RepoID = repoID
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if input.CurrentUserID == input.UserId {
		err := errors.New("cannot invite your self")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := h.repoUseCase.InviteRepoMember(ctx, input); err != nil {
		span.SetStatus(codes.Error, "failed to invite repo member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("user invited to repository successfully")
	span.SetStatus(codes.Ok, "user invited to repository successfully")
	c.JSON(http.StatusOK, gin.H{"message": "User invited to project successfully"})

}

// InviteUsers godoc
//
//	@Summary	Invite users to repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string						true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string						true	"Repository namespace"
//	@Param		repo_name	path		string						true	"Repository name"
//	@Param		model		body		dto.InviteRepoMembersInput	true	"Invite User to Repository information"
//	@Success	200			{object}	string						"Return message invite organization"
//	@Failure	400			{object}	dto.HTTPError				"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError				"Not Found"
//	@Failure	500			{object}	dto.HTTPError				"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/invite [post]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) InviteUsers(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.InviteUsers")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repo id from context")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	var input dto.InviteRepoMembersInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind request body")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input.RepoID = repoID
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	if err := h.repoUseCase.InviteMultipleRepoMembers(ctx, input); err != nil { // Call the *plural* InviteRepoMembers use case function
		span.SetStatus(codes.Error, "failed to invite users to repository")
		span.RecordError(err)
		if errors.Is(err, usecase.ErrUserNotFound) || errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	span.AddEvent("invite users to repository successfully")
	span.SetStatus(codes.Ok, "invite users to repository successfully")
	c.JSON(http.StatusOK, gin.H{"message": "Users invited to project successfully"})
}

// GetMember godoc
//
//	@Summary	Get Member in Repository
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		member_id	path		string			true	"Member id"
//	@Failure	403			{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/members/{member_id} [get]
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetRepositoryMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetRepositoryMember")
	defer span.End()

	// _, err := GetCurrentUserId(ParseContext(c))
	// if err != nil {
	// 	span.SetStatus(codes.Error, "failed to get current user id")
	// 	span.RecordError(err)
	// 	dto.ErrorResponse(c, dto.NewForbiddenError(err))
	// 	return
	// }

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "invalid member ID format")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	input := dto.GetMemberRepositoryInput{
		RepoID:   repoID,
		MemberId: memberId,
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	member, err := h.repoUseCase.GetMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get repository member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository member retrieved successfully")
	span.SetStatus(codes.Ok, "repository member retrieved successfully")
	c.JSON(http.StatusOK, member)
}

// RemoveRepositoryMember godoc
//
//	@Summary	Remove Member in Repository
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		member_id	path		string			true	"Member id"
//	@Failure	403			{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/members/{member_id} [delete]
//	@Security	Bearer
func (h *repositoryHandlerImpl) RemoveRepositoryMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.RemoveRepositoryMember")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "invalid member ID format")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if memberId == currentUserId {
		err := errors.New("cannot remove yourself")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, err.Error()))
		return
	}

	input := dto.RemoveMemberRepositoryInput{
		RepoID:        repoID,
		MemberId:      memberId,
		CurrentUserID: currentUserId,
	}
	// if err := validator.Validate(input); err != nil {
	// 	span.SetStatus(codes.Error, "failed to validate input")
	// 	span.RecordError(err)
	// 	dto.ErrorResponse(c, dto.NewBadRequestError(err))
	// 	return
	// }

	err = h.repoUseCase.RemoveMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to remove repository member")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository member removed successfully")
	span.SetStatus(codes.Ok, "repository member removed successfully")
	c.JSON(http.StatusOK, gin.H{
		"message": "Remove member successfully",
	})
}

// UpdateRepositoryMember godoc
//
//	@Summary	Update Member in Repository
//	@Tags		Organization
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Param		member_id	path		string							true	"Member id"
//	@Param		model		body		dto.UpdateMemberRepositoryInput	true	"Invite User to Organization information"
//	@Failure	403			{object}	dto.HTTPError					"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError					"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/members/{member_id} [put]
//	@Security	Bearer
func (h *repositoryHandlerImpl) UpdateRepositoryMember(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.UpdateRepositoryMember")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "failed to get current user id"))
		return
	}

	var input dto.UpdateMemberRepositoryInput
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	memberId, err := uuid.Parse(c.Param("member_id"))
	if err != nil {
		span.SetStatus(codes.Error, "invalid member ID format")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if memberId == currentUserId {
		err := errors.New("cannot update yourself")
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "cannot update yourself", err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err, "cannot update yourself"))
		return
	}

	input.RepoID = repoID
	input.MemberId = memberId
	input.CurrentUserID = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	err = h.repoUseCase.UpdateMember(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		otelzap.ErrorWithContext(ctx, "failed to update repository member", err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository member updated successfully")
	span.SetStatus(codes.Ok, "repository member updated successfully")
	c.JSON(http.StatusOK, gin.H{
		"message": "Update member successfully",
	})
}

// ListRepoTags godoc
//
//	@Summary	Get repository tags
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		sub_type	query		string					false	"Tag sub-type"
//	@Param		type		query		string					false	"Tag type"
//	@Param		repo_type	query		enums.RepoType			false	"Repository type"
//	@Param		key_word	query		string					false	"Key word"
//	@Success	200			{object}	dto.ListRepoTagsOutput	"Ok"
//	@Failure	400			{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError			"Not Found"
//	@Failure	500			{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoTags(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ctx.Request.Context(), "handlers.repository.ListRepoTags")
	defer span.End()

	var input dto.ListRepoTagsInput
	if err := ctx.ShouldBind(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate request")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	output, err := h.repoUseCase.ListRepoTags(c, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository tags")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository tags listed successfully")
	span.SetStatus(codes.Ok, "repository tags listed successfully")
	ctx.JSON(http.StatusOK, output)
}

// PushEventWebhookHandler handles incoming GitLab push event webhooks.
// It parses the webhook payload, checks if the event is for the main branch,
// and triggers an update of repository tags if applicable.
//
// Parameters:
//   - ctx: The Gin context for the HTTP request.
func (h *repositoryHandlerImpl) PushEventWebhookHandler(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ctx.Request.Context(), "handlers.repository.PushEventWebhookHandler")
	defer span.End()

	var payload dto.GitLabPushEvent
	if err := ctx.ShouldBindJSON(&payload); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON payload")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewBadRequestError(err))
		return
	}

	if !strings.HasSuffix(payload.Ref, "/main") {
		span.AddEvent("ignoring push event, not on main branch")
		otelzap.InfoWithContext(c, "ignoring push event, not on main branch")
		ctx.JSON(http.StatusOK, gin.H{"message": "Ignored push event. Not on main branch."})
		return
	}

	err := h.repoUseCase.UpdateTagsInRepository(ctx, payload)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update tags in repository")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewInternalError(err))
		return
	}

	span.AddEvent("webhook handled successfully")
	span.SetStatus(codes.Ok, "webhook handled successfully")
	ctx.JSON(http.StatusOK, gin.H{"message": "Callback handle successfully"})
}

// ListRepoTemplates godoc
//
//	@Summary	Get repository templates
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Success	200	{object}	dto.ListRepoTagsOutput	"Ok"
//	@Failure	400	{object}	dto.HTTPError			"Bad Request - invalid request"
//	@Failure	404	{object}	dto.HTTPError			"Not Found"
//	@Failure	500	{object}	dto.HTTPError			"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/tags [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ListRepoTemplates(ctx *gin.Context) {
	c, span := oteltrace.Tracer.Start(ctx.Request.Context(), "handlers.repository.ListRepoTemplates")
	defer span.End()

	output, err := h.repoUseCase.ListRepoTemplates(c)
	if err != nil {
		span.SetStatus(codes.Error, "failed to list repository templates")
		span.RecordError(err)
		dto.ErrorResponse(ctx, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("repository templates listed successfully")
	span.SetStatus(codes.Ok, "repository templates listed successfully")
	ctx.JSON(http.StatusOK, output)
}

// GetEnv godoc
//
//	@Summary	Get environment variables in Repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Failure	403			{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/envs [get]
//	@Security	Bearer
func (h *repositoryHandlerImpl) GetEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.GetEnv")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	resp, err := h.repoUseCase.GetEnv(ctx, repoID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to get environment variables")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("environment variables retrieved successfully")
	span.SetStatus(codes.Ok, "environment variables retrieved successfully")
	c.JSON(http.StatusOK, resp)
}

// CreateEnv godoc
//
//	@Summary	Create environment variables in Repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		model		body		object			true	"Request body - can be either CreateRepositoryEnvRequest for single env or BulkCreateRepositoryEnvRequest for multiple envs"
//	@Failure	403			{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/envs [post]
//	@Security	Bearer
func (h *repositoryHandlerImpl) CreateEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.CreateEnv")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	// Try to parse as bulk request first
	var bulkInput dto.BulkCreateRepositoryEnvRequest
	var rawBody []byte
	if rawBody, err = c.GetRawData(); err != nil {
		span.SetStatus(codes.Error, "failed to read request body")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, "failed to read request body"))
		return
	}

	// Try to unmarshal as bulk request
	if err := json.Unmarshal(rawBody, &bulkInput); err == nil && len(bulkInput.Envs) > 0 {
		// Handle bulk request
		if err := validator.Validate(bulkInput); err != nil {
			span.SetStatus(codes.Error, "failed to validate bulk input")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewBadRequestError(err))
			return
		}

		err = h.repoUseCase.BulkCreateEnv(ctx, repoID, bulkInput)
		if err != nil {
			span.SetStatus(codes.Error, "failed to bulk create environment variables")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
	} else {
		// Handle single request
		var input dto.CreateRepositoryEnvRequest
		if err := json.Unmarshal(rawBody, &input); err != nil {
			span.SetStatus(codes.Error, "failed to bind JSON input")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewBadRequestError(err))
			return
		}
		if err := validator.Validate(input); err != nil {
			span.SetStatus(codes.Error, "failed to validate input")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewBadRequestError(err))
			return
		}

		err = h.repoUseCase.CreateEnv(ctx, repoID, input)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create environment variables")
			span.RecordError(err)
			dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
			return
		}
	}

	span.AddEvent("create environment variables successfully")
	span.SetStatus(codes.Ok, "create environment variables successfully")
	c.JSON(http.StatusNoContent, nil)
}

// UpdateEnv godoc
//
//	@Summary	Update environment variables in Repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string							true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string							true	"Repository namespace"
//	@Param		repo_name	path		string							true	"Repository name"
//	@Param		model		body		dto.UpdateRepositoryEnvRequest	true	"Request body"
//	@Failure	403			{object}	dto.HTTPError					"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError					"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/envs [put]
//	@Security	Bearer
func (h *repositoryHandlerImpl) UpdateEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.UpdateEnv")
	defer span.End()

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	var input dto.UpdateRepositoryEnvRequest
	if err := c.ShouldBindJSON(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind JSON input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	err = h.repoUseCase.UpdateEnv(ctx, repoID, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to update environment variables")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("environment variables updated successfully")
	span.SetStatus(codes.Ok, "environment variables updated successfully")
	c.JSON(http.StatusNoContent, nil)
}

// DeleteEnv godoc
//
//	@Summary	Delete environment variables in Repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		key			query		string			true	"Request body"
//	@Failure	403			{object}	dto.HTTPError	"Forbidden - invalid token"
//	@Failure	500			{object}	dto.HTTPError	"Internal Host Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/envs [delete]
//	@Security	Bearer
func (h *repositoryHandlerImpl) DeleteEnv(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.UpdateEnv")
	defer span.End()

	var input dto.DeleteRepositoryEnvRequest
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	err = h.repoUseCase.DeleteEnv(ctx, repoID, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete environment variables")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewErrorFromUsecase(err))
		return
	}

	span.AddEvent("delete environment variables successfully")
	span.SetStatus(codes.Ok, "delete environment variables successfully")
	c.JSON(http.StatusNoContent, nil)
}

// ArchiveRepository godoc
//
//	@Summary	Archive repository
//	@Tags		Repository
//	@Accept		json
//	@Produce	json
//	@Param		repo_type	path		string			true	"Repository type"	Enums(spaces, datasets, models, composes)
//	@Param		namespace	path		string			true	"Repository namespace"
//	@Param		repo_name	path		string			true	"Repository name"
//	@Param		type		query		string			false	"Archive type"
//	@Param		ref			query		string			false	"Branch name"
//	@Failure	400			{object}	dto.HTTPError	"Bad Request - invalid request"
//	@Failure	404			{object}	dto.HTTPError	"Not Found"
//	@Failure	500			{object}	dto.HTTPError	"Internal Server Error - Encountered an unexpected condition"
//	@Router		/repositories/{repo_type}/{namespace}/{repo_name}/archive [get]
//
//	@Security	Bearer
func (h *repositoryHandlerImpl) ArchiveRepository(c *gin.Context) {
	ctx, span := oteltrace.Tracer.Start(c.Request.Context(), "handlers.repository.ArchiveRepository")
	defer span.End()

	currentUserId, err := GetCurrentUserId(ParseContext(c))
	if err != nil {
		span.SetStatus(codes.Error, "failed to get current user id")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewForbiddenError(err))
		return
	}

	var input dto.ArchiveRepositoryInput
	if err := c.ShouldBindQuery(&input); err != nil {
		span.SetStatus(codes.Error, "failed to bind query parameters")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	repoID, err := types.RepoID{}.FromGinContext(c)
	if err != nil {
		span.SetStatus(codes.Error, "invalid repository ID")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err, err.Error()))
		return
	}

	input.RepoID = repoID
	input.CurrentUserId = currentUserId
	if err := validator.Validate(input); err != nil {
		span.SetStatus(codes.Error, "failed to validate input")
		span.RecordError(err)
		dto.ErrorResponse(c, dto.NewBadRequestError(err))
		return
	}

	gitlabResp, err := h.repoUseCase.ArchiveRepo(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to archive repository")
		span.RecordError(err)

		if errors.Is(err, usecase.ErrRepositoryNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		if errors.Is(err, usecase.ErrUserNotFound) {
			dto.ErrorResponse(c, dto.NewNotFoundError(err))
			return
		}

		dto.ErrorResponse(c, dto.NewInternalError(err))
		return
	}

	for key, value := range gitlabResp.Header {
		if len(value) > 0 {
			c.Header(key, value[0])
		}
	}

	// Forward the body from GitLab response to Gin response
	c.DataFromReader(gitlabResp.StatusCode, gitlabResp.ContentLength, gitlabResp.Header.Get("content-type"), gitlabResp.Body, nil)

	span.AddEvent("repository archived successfully")
	span.SetStatus(codes.Ok, "repository archived successfully")
}
