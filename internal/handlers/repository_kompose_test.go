package handlers_test

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/internal/dto"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository/mocks"
)

func TestGetComposeServices(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Composes
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		queryParams   map[string]string
	}{
		{
			name:          "should return error if usecase fails",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetListComposeServices", mock.Anything, mock.Anything).
					Return(nil, errors.New("internal error"))
			},
			expectErr: errors.New("Internal error"),
		},
		{
			name:          "should return error if repository not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetListComposeServices", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
		},
		{
			name:          "should return compose services successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				services := []dto.ComposeService{
					{
						ID:            uuid.New(),
						Name:          "web-service",
						Author:        "testuser",
						Image:         "nginx:latest",
						Ports:         []string{"80:80"},
						Volumes:       []string{"/data:/app/data"},
						Status:        "Running",
						RestartPolicy: "always",
						RepoID:        repoID,
						URL:           "https://web-service.example.com",
						PrivateURL:    "http://web-service.default.svc.cluster.local",
						NodeName:      "node-1",
						ProxyBodySize: 10,
					},
				}
				response := &dto.GetComposeServicesResponse{
					Data: &services,
					Pagination: &dto.Pagination{
						Total:    1,
						PageNo:   1,
						PageSize: 10,
					},
				}
				d.usecase.On("GetListComposeServices", mock.Anything, mock.Anything).
					Return(response, nil).Once()
			},
		},
		{
			name:     "should return compose services successfully with search parameter",
			ctxValue: uuid.New(),
			queryParams: map[string]string{
				"search": "web",
			},
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				services := []dto.ComposeService{
					{
						ID:            uuid.New(),
						Name:          "web-service",
						Author:        "testuser",
						Image:         "nginx:latest",
						Ports:         []string{"80:80"},
						Volumes:       []string{"/data:/app/data"},
						Status:        "Running",
						RestartPolicy: "always",
						RepoID:        repoID,
						URL:           "https://web-service.example.com",
						PrivateURL:    "http://web-service.default.svc.cluster.local",
						NodeName:      "node-1",
						ProxyBodySize: 10,
					},
				}
				response := &dto.GetComposeServicesResponse{
					Data: &services,
					Pagination: &dto.Pagination{
						Total:    1,
						PageNo:   1,
						PageSize: 10,
					},
				}
				d.usecase.On("GetListComposeServices", mock.Anything, mock.MatchedBy(func(req dto.GetComposeServicesRequest) bool {
					return req.Search != nil && *req.Search == "web"
				})).Return(response, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			if tt.ctxValue != uuid.Nil {
				c.Set("user_id", tt.ctxValue)
			}

			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}

			// Set up query parameters
			c.Request = httptest.NewRequest("GET", "/repositories/composes/namespace/repo-name/composes", nil)
			if tt.queryParams != nil {
				query := c.Request.URL.Query()
				for key, value := range tt.queryParams {
					query.Add(key, value)
				}
				c.Request.URL.RawQuery = query.Encode()
			}

			h.GetComposeServices(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}
