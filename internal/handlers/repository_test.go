package handlers_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"api-server/configs"
	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	"api-server/internal/handlers"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/usecase/repository/mocks"
	"api-server/internal/utils"
)

func TestCreateRepository(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoId := uuid.New()
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	owner := dto.RepositoryOwner{
		ID:   uuid.New(),
		Type: enums.RepoOwnerType_User,
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		input         dto.RepositoryCreateInput
		expStatusCode int
	}{
		{
			name: "should return error if validating name fail",
			input: dto.RepositoryCreateInput{
				Name:       "name with space",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 400,
			mockFn:        func(d *dependencies) {},
			expectErr: errors.New(
				"Name must start with a letter or number, may contain 1 underscore or hyphen between words, and must be between 2 and 50 characters long.",
			),
		},
		{
			name: "should return error if create space repository without hardware",
			input: dto.RepositoryCreateInput{
				Name:       "create",
				Type:       enums.RepoType_Spaces,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 400,
			mockFn:        func(d *dependencies) {},
			expectErr: errors.New(
				"Create is a reserved keyword. Please choose different input",
			),
		},
		{
			name: "should return error if create space repository without hardware",
			input: dto.RepositoryCreateInput{
				Name:       "name",
				Type:       enums.RepoType_Spaces,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 400,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Hardware is required for Space repository"),
		},
		{
			name: "should return usecase.ErrRepositoryExist",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 400,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryExist)
			},
			expectErr: usecase.ErrRepositoryExist,
		},
		{
			name: "should return usecase.ErrOrganizationNotFound",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 404,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrOrganizationNotFound)
			},
			expectErr: usecase.ErrOrganizationNotFound,
		},
		{
			name: "should return usecase.ErrUserNotFound",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expectErr: errors.New("User not found"),
		},
		{
			name: "should return error if create repository fail",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 500,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepository", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			expStatusCode: 403,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name: "should create repository successfully",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType_Models,
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 201,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepository", mock.Anything, mock.Anything).
					Return(&dto.RepositoryCreateResponse{
						ID: repoId,
					}, nil)
			},
		},
		{
			name: "should create repository successfully",
			input: dto.RepositoryCreateInput{
				Name:       "test",
				Type:       enums.RepoType("sample"),
				Owner:      owner,
				Visibility: enums.RepoVisibility_Internal,
			},
			ctxValue:      uuid.New(),
			expStatusCode: 400,
			mockFn: func(d *dependencies) {
			},
			expectErr: errors.New("Key: 'RepositoryCreateInput.Type' Error:Field validation for 'Type' failed on the 'oneof' tag"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				"POST",
				"/repositories",
				strings.NewReader(string(jsonInput)),
			)

			h.AddRepo(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]any
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestUpdateRepo(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		ctxValue      uuid.UUID
		input         dto.UpdateRepositoryInput
		expectErr     error
		expStatusCode int
	}{
		{
			name: "should return error when GetCurrentUserId returns error",
			input: dto.UpdateRepositoryInput{
				Hardware: &dto.RepositoryHardwareInput{
					Name:     "name",
					NodeName: "node-name",
				},
			},
			expStatusCode: 403,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name: "should create repository fail",
			input: dto.UpdateRepositoryInput{
				Hardware: &dto.RepositoryHardwareInput{
					Name:     "name",
					NodeName: "node-name",
				},
			},
			ctxValue:      uuid.New(),
			expStatusCode: 500,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRepository", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name: "should create repository successfully",
			input: dto.UpdateRepositoryInput{
				Hardware: &dto.RepositoryHardwareInput{
					Name:     "name",
					NodeName: "node-name",
				},
			},
			ctxValue:      uuid.New(),
			expStatusCode: 200,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateRepository", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
			}
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				"PATCH",
				"/repositories",
				strings.NewReader(string(jsonInput)),
			)

			h.UpdateRepo(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestDeleteRepo(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name:          "should return error if delete repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteRepository", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should delete repository successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteRepository", mock.Anything, mock.Anything).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("DELETE", "/repositories", nil)
			h.DeleteRepo(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetRepoInfo(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     *dto.GetRepositoryOutput
		invalidRepoID bool
	}{
		{
			name:          "should return error if get info repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRepoInfo", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get info repository successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRepoInfo", mock.Anything, mock.Anything).
					Return(&dto.GetRepositoryOutput{
						SSHURL: "ssh://gitlab.com",
					}, nil)
			},
			expOutput: &dto.GetRepositoryOutput{
				SSHURL: "ssh://gitlab.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories", nil)
			h.GetRepoInfo(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepos(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		ctxValue      uuid.UUID
		input         dto.GetRepositoriesInput
		expectErr     error
		expStatusCode int
	}{
		{
			name:     "should return if fail validate Keyword",
			ctxValue: uuid.New(),
			mockFn:   func(d *dependencies) {},
			input: dto.GetRepositoriesInput{
				Keyword: strings.Repeat("a", 101),
			},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
			),
		},
		{
			name:          "should return error if list repositories fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetAllRepositories", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		// {
		// 	name:          "should return error when GetCurrentUserId returns error",
		// 	expStatusCode: http.StatusForbidden,
		// 	mockFn:        func(d *dependencies) {},
		// 	expectErr:     errors.New("Forbidden"),
		// },
		{
			name:          "should list repositories successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetAllRepositories", mock.Anything, mock.Anything).
					Return(&dto.GetRepositoriesOutput{}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest("GET", "/repositories", nil)
			query := c.Request.URL.Query()
			query.Add("keyword", tt.input.Keyword)
			c.Request.URL.RawQuery = query.Encode()

			h.ListRepos(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetFileContent(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		paramInput    dto.GetFileFromRepositoryInput
		ctxValue      uuid.UUID
		expStatusCode int
	}{
		{
			name:          "should return error if fobidden",
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
			expStatusCode: http.StatusForbidden,
		},
		{
			name: "Invalid param file_path",
			expectErr: errors.New(
				"Key: 'GetFileFromRepositoryInput.Path' Error:Field validation for 'Path' failed on the 'required' tag",
			),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "",
			},
		},
		{
			name:          "should return error if get file contents fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New(http.StatusText(http.StatusInternalServerError)),
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "path",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if get file contents repo not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "path",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if get file contents current user not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expectErr: errors.New("User not found"),
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "path",
				Ref:    "main",
			},
		},
		{
			name:          "should get file contents successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetFileFromRepository", mock.Anything, mock.Anything).Return(nil, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "path",
				Ref:    "main",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Params = gin.Params{
				{Key: enums.REPO_TYPE, Value: repoType.String()},
				{Key: enums.NAMESPACE, Value: namespace},
				{Key: enums.REPO_NAME, Value: repoName},
				{Key: "file_path", Value: tt.paramInput.Path},
			}
			c.Request = httptest.NewRequest(
				"GET",
				fmt.Sprintf("/repositories/%s/files/%s", repoID.String(), tt.paramInput.Path),
				nil,
			)

			h.GetFileContent(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoBranches(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     []dto.RepositoryBranchInfo
		invalidRepoID bool
	}{
		{
			name:          "should return error if get info repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoBranches", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get info repository successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoBranches", mock.Anything, mock.Anything).
					Return([]dto.RepositoryBranchInfo{
						{RepositoryBranch: dto.RepositoryBranch{
							Name: "main",
						}},
					}, nil)
			},
			expOutput: []dto.RepositoryBranchInfo{
				{RepositoryBranch: dto.RepositoryBranch{
					Name: "main",
				}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_id/branches", nil)
			h.ListRepoBranches(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response []dto.RepositoryBranchInfo
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoFiles(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     []dto.RepositoryFile
		invalidRepoID bool
	}{
		{
			name:          "should return error if get info repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoFiles", mock.Anything, mock.Anything).Return(nil, nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get repository files successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoFiles", mock.Anything, mock.Anything).
					Return([]dto.RepositoryFile{
						{
							Name: "main.go",
							Type: "blob",
						},
					}, nil, nil)
				d.usecase.On("GetLastRepoCommit", mock.Anything, mock.Anything).
					Return(&dto.RepositoryCommit{}, nil).
					Once()
			},
			expOutput: []dto.RepositoryFile{
				{
					Name:       "main.go",
					Type:       "blob",
					LastCommit: &dto.RepositoryCommit{},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_id/files", nil)
			h.ListRepoFiles(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response []dto.RepositoryBranchInfo
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoCommits(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     *dto.GetRepositoryCommitsOutput
		invalidRepoID bool
	}{
		{
			name:          "should return error if get info repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoCommits", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get repository commits successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoCommits", mock.Anything, mock.Anything).
					Return(&dto.GetRepositoryCommitsOutput{
						Data: &[]dto.RepositoryCommit{
							{
								ID:      "1",
								ShortID: "1234",
							},
						},
					}, nil)
			},
			expOutput: &dto.GetRepositoryCommitsOutput{
				Data: &[]dto.RepositoryCommit{
					{
						ID:      "1",
						ShortID: "1234",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_id/commits", nil)
			h.ListRepoCommits(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response dto.GetRepositoryCommitsOutput
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoContributors(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     []dto.RepositoryContributor
		invalidRepoID bool
	}{
		{
			name:          "should return error if get info repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoContributors", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should get repository contributors successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoContributors", mock.Anything, mock.Anything).
					Return([]dto.RepositoryContributor{
						{
							Name:  "Admin",
							Email: "<EMAIL>",
						},
					}, nil)
			},
			expOutput: []dto.RepositoryContributor{
				{
					Name:  "Admin",
					Email: "<EMAIL>",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_id/contributors", nil)
			h.ListRepoContributors(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response []dto.RepositoryBranchInfo
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoMembers(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		input         dto.ListRepositoryMembersInput
		ctxValue      uuid.UUID
		expStatusCode int
		expectErr     error
		expOutput     dto.ListRepositoryMembersOutput
		invalidRepoID bool
	}{
		{
			name:          "should return error when GetCurrentUserId returns error",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:     "should return error if validating Keyword fail",
			ctxValue: uuid.New(),
			mockFn:   func(d *dependencies) {},
			input: dto.ListRepositoryMembersInput{
				Keyword: strings.Repeat("a", 101),
			},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long",
			),
		},
		{
			name:     "should return if list repo members fail",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoMembers", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			input: dto.ListRepositoryMembersInput{
				Keyword: "keyword",
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name:     "should list repo members successfully",
			ctxValue: uuid.New(),
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoMembers", mock.Anything, mock.Anything).
					Return(&dto.ListRepositoryMembersOutput{}, nil)
			},
			input: dto.ListRepositoryMembersInput{
				Keyword: "keyword",
			},
			expStatusCode: http.StatusOK,
			expOutput:     dto.ListRepositoryMembersOutput{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/:repo_id/members", nil)
			query := c.Request.URL.Query()
			query.Add("keyword", tt.input.Keyword)
			c.Request.URL.RawQuery = query.Encode()

			h.ListRepoMembers(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response dto.ListRepositoryMembersOutput
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetHeaderRawFileContent(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)
	currentUserID, _ := uuid.Parse("75186f82-2af2-4f02-8070-c1f3b621bdd1")

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		paramInput    dto.GetFileFromRepositoryInput
		ctxValue      uuid.UUID
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name:          "should return error if forbidden",
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
			expStatusCode: http.StatusForbidden,
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "Invalid param file_path",
			expectErr: errors.New(
				"Key: 'GetFileFromRepositoryInput.Path' Error:Field validation for 'Path' failed on the 'required' tag",
			),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "",
			},
		},
		{
			name:          "should return error if get file contents fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if get file contents repo not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if current user not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should download file successfully with LFS = false",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, dto.GetFileFromRepositoryInput{
					RepoID:        repoID,
					Path:          "sample",
					Ref:           "main",
					LFS:           utils.Ptr(false),
					CurrentUserId: currentUserID,
				}).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
				LFS:    utils.Ptr(false),
			},
		},
		{
			name:          "should download file successfully with LFS = true",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, dto.GetFileFromRepositoryInput{
					RepoID:        repoID,
					Path:          "sample",
					LFS:           utils.Ptr(true),
					Ref:           "main",
					CurrentUserId: currentUserID,
				}).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
				LFS:    utils.Ptr(true),
			},
		},
		{
			name:          "should get file contents successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetHeaderRawFileFromRepository", mock.Anything, mock.Anything).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "file_path", Value: tt.paramInput.Path},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "file_path", Value: tt.paramInput.Path},
				}
			}
			c.Request = httptest.NewRequest(
				"HEAD",
				"/repositories/:repo_id/files/:file_path/raw",
				nil,
			)
			query := c.Request.URL.Query()
			query.Add("ref", tt.paramInput.Ref)
			if tt.paramInput.LFS != nil {
				query.Add("lfs", strconv.FormatBool(*tt.paramInput.LFS))
			}
			c.Request.URL.RawQuery = query.Encode()

			h.GetHeaderRawFileContent(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				// var response dto.HTTPError
				// err := json.Unmarshal(w.Body.Bytes(), &response)
				// assert.NoError(t, err)
				// assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				// var response *http.Response
				// err := json.Unmarshal(w.Body.Bytes(), &response)
				// assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetRawFileContent(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)
	currentUserID, _ := uuid.Parse("75186f82-2af2-4f02-8070-c1f3b621bdd1")

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		paramInput    dto.GetFileFromRepositoryInput
		ctxValue      uuid.UUID
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name:          "should return error if forbidden",
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
			expStatusCode: http.StatusForbidden,
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "Invalid param file_path",
			expectErr: errors.New(
				"Key: 'GetFileFromRepositoryInput.Path' Error:Field validation for 'Path' failed on the 'required' tag",
			),
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "",
			},
		},
		{
			name:          "should return error if get file contents fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if get file contents repo not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if current user not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetRawFileFromRepository", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
		{
			name:          "should download file successfully with LFS = false",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetRawFileFromRepository", mock.Anything, dto.GetFileFromRepositoryInput{
					RepoID:        repoID,
					Path:          "sample",
					Ref:           "main",
					LFS:           utils.Ptr(false),
					CurrentUserId: currentUserID,
				}).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
				LFS:    utils.Ptr(false),
			},
		},
		{
			name:          "should download file successfully with LFS = true",
			ctxValue:      currentUserID,
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetRawFileFromRepository", mock.Anything, dto.GetFileFromRepositoryInput{
					RepoID:        repoID,
					Path:          "sample",
					LFS:           utils.Ptr(true),
					Ref:           "main",
					CurrentUserId: currentUserID,
				}).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
				LFS:    utils.Ptr(true),
			},
		},
		{
			name:          "should get file contents successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				httpResp := http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header: http.Header{
						"Content-Type": []string{"application/json"},
					},
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}
				d.usecase.On("GetRawFileFromRepository", mock.Anything, mock.Anything).
					Return(&httpResp, nil)
			},
			paramInput: dto.GetFileFromRepositoryInput{
				RepoID: repoID,
				Path:   "sample",
				Ref:    "main",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "file_path", Value: tt.paramInput.Path},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "file_path", Value: tt.paramInput.Path},
				}
			}
			c.Request = httptest.NewRequest(
				"GET",
				"/repositories/:repo_id/files/:file_path/raw",
				nil,
			)
			query := c.Request.URL.Query()
			query.Add("ref", tt.paramInput.Ref)
			if tt.paramInput.LFS != nil {
				query.Add("lfs", strconv.FormatBool(*tt.paramInput.LFS))
			}
			c.Request.URL.RawQuery = query.Encode()

			h.GetRawFileContent(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response *http.Response
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetSingleRepoBranch(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		input         dto.GetSingleRepositoryBranchInput
		mockFn        func(d *dependencies)
		ctxValue      uuid.UUID
		expStatusCode int
		expectErr     error
		expOutput     *dto.RepositoryBranchInfo
		invalidRepoID bool
	}{
		{
			name: "should return error if GetCurrentUserId fail",
			input: dto.GetSingleRepositoryBranchInput{
				Branch: "main",
			},
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "should return error if validating branch",
			input: dto.GetSingleRepositoryBranchInput{
				Branch: "",
			},
			ctxValue:      uuid.New(),
			mockFn:        func(d *dependencies) {},
			expStatusCode: http.StatusBadRequest,
			expectErr: errors.New(
				"Key: 'GetSingleRepositoryBranchInput.Branch' Error:Field validation for 'Branch' failed on the 'required' tag",
			),
		},
		{
			name: "should return error if get repository commits fail",
			input: dto.GetSingleRepositoryBranchInput{
				Branch: "main",
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetSingleRepoBranch", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name: "should get repository commits successfully",
			input: dto.GetSingleRepositoryBranchInput{
				Branch: "main",
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetSingleRepoBranch", mock.Anything, mock.Anything).
					Return(&dto.RepositoryBranchInfo{}, nil)
			},
			expOutput: &dto.RepositoryBranchInfo{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)

			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "branch", Value: tt.input.Branch},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "branch", Value: tt.input.Branch},
				}
			}
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				"GET",
				"/repositories/:repo_id/commits",
				strings.NewReader(string(jsonInput)),
			)
			h.GetSingleRepoBranch(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response *dto.RepositoryBranchInfo
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestCreateRepoCommit(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	// repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	content := "This is the content of the new file"
	tests := []struct {
		name          string
		input         dto.CreateRepositoryCommitInput
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     *dto.RepositoryCommit
		invalidRepoID bool
	}{
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "should return error if get info repository fail",
			input: dto.CreateRepositoryCommitInput{
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "add new file",
					Actions: []dto.GitLabAction{
						{
							Action:   "create",
							FilePath: "new_file.txt",
							Content:  &content,
						},
					},
				},
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepoCommit", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name: "should get repository commits successfully",
			input: dto.CreateRepositoryCommitInput{
				RepoCommitInput: dto.RepoCommitInput{
					Branch:        "main",
					CommitMessage: "add new file",
					Actions: []dto.GitLabAction{
						{
							Action:   "create",
							FilePath: "new_file.txt",
							Content:  &content,
						},
					},
				},
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateRepoCommit", mock.Anything, mock.Anything).
					Return(&dto.RepositoryCommit{
						ID:      "1",
						ShortID: "1234",
					}, nil)
			},
			expOutput: &dto.RepositoryCommit{
				ID:      "1",
				ShortID: "1234",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			jsonInput, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				"POST",
				"/repositories/:repo_id/commits",
				strings.NewReader(string(jsonInput)),
			)
			h.CreateRepoCommit(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response *dto.RepositoryBranchInfo
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestInviteRepoMember(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}
	userId := uuid.New()
	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name          string
		input         dto.InviteRepoMemberInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name: "should return error when InviteUser returns error",
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   uuid.New(),
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteRepoMember", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "should return error when self invite",
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   userId,
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
			},
			ctxValue:      userId,
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Cannot invite your self"),
		},
		{
			name: "should invite user successfully",
			input: dto.InviteRepoMemberInput{
				RepoID:   repoID,
				UserId:   uuid.New(),
				Role:     "owner",
				ExpireAt: &expireTime,
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteRepoMember", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockRepoUsecase),
			}
			tt.mockFn(&d)
			handler := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/repositories/:repo_id/invite",
				strings.NewReader(string(inputJSON)),
			)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}

			handler.InviteUser(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestInviteMultiRepoMember(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	expireTime := time.Date(2026, 1, 2, 15, 4, 5, 0, time.UTC)
	tests := []struct {
		name          string
		input         dto.InviteRepoMembersInput
		ctxValue      uuid.UUID
		mockFn        func(d *dependencies)
		expectErr     error
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name: "should return error when InviteUser returns error",
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleRepoMembers", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name: "should invite users successfully",
			input: dto.InviteRepoMembersInput{
				RepoID: repoID,
				Members: []dto.RepoMemberInviteInput{
					{
						UserId:   uuid.New(),
						Role:     "owner",
						ExpireAt: &expireTime,
					},
				},
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("InviteMultipleRepoMembers", mock.Anything, mock.Anything).Return(nil)
			},
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			expectErr:     nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := dependencies{
				usecase: new(mocks.MockRepoUsecase),
			}
			tt.mockFn(&d)

			handler := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin Context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			inputJSON, _ := json.Marshal(tt.input)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/repositories/:repo_id/invite",
				strings.NewReader(string(inputJSON)),
			)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}

			handler.InviteUsers(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoTags(t *testing.T) {
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		input         dto.ListRepoTagsInput
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     *dto.ListRepoTagsOutput
	}{
		{
			name:          "should return error if invalid keyword",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			input: dto.ListRepoTagsInput{
				RepoType: "models",
				Keyword:  "$",
			},
			mockFn:    func(d *dependencies) {},
			expectErr: errors.New("Keyword must start and end with a letter or number, may contain letters, numbers, underscores, spaces, at and must be between 1 and 100 characters long"),
		},
		{
			name:          "should return error if get repo tags fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			input: dto.ListRepoTagsInput{
				RepoType: "models",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoTags", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should list repository tags successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			input: dto.ListRepoTagsInput{
				RepoType: "models",
			},
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoTags", mock.Anything, mock.Anything).
					Return(&dto.ListRepoTagsOutput{
						Data: &[]dto.Tag{
							{
								Name:  "Json",
								Value: "json",
								Type:  "task",
							},
						},
					}, nil)
			},
			expOutput: &dto.ListRepoTagsOutput{
				Data: &[]dto.Tag{
					{
						Name:  "Json",
						Value: "json",
						Type:  "task",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest("GET", "/repositories/tags", nil)
			query := c.Request.URL.Query()
			query.Add("repo_type", tt.input.RepoType)
			query.Add("keyword", tt.input.Keyword)
			c.Request.URL.RawQuery = query.Encode()

			h.ListRepoTags(c)
			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response dto.ListRepoTagsOutput
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestArchiveRepository(t *testing.T) {
	gin.SetMode(gin.TestMode)

	repoType := enums.RepoType_Models
	namespace := "namespace"
	repoName := "repo-name"
	repoID := *types.NewRepoID(repoType, namespace, repoName)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		paramInput    dto.ArchiveRepositoryInput
		ctxValue      uuid.UUID
		expStatusCode int
		invalidRepoID bool
	}{
		{
			name:          "should return error if fobidden",
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Forbidden"),
			expStatusCode: http.StatusForbidden,
		},
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error if archive repository current user not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("ArchiveRepo", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expectErr: usecase.ErrUserNotFound,
			paramInput: dto.ArchiveRepositoryInput{
				RepoID: repoID,
				Type:   "tar",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if archive repository return repo not found",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusNotFound,
			mockFn: func(d *dependencies) {
				d.usecase.On("ArchiveRepo", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expectErr: usecase.ErrRepositoryNotFound,
			paramInput: dto.ArchiveRepositoryInput{
				RepoID: repoID,
				Type:   "tar",
				Ref:    "main",
			},
		},
		{
			name:          "should return error if archive repository fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ArchiveRepo", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
			paramInput: dto.ArchiveRepositoryInput{
				RepoID: repoID,
				Type:   "tar",
				Ref:    "main",
			},
		},
		{
			name:          "should archive repository successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				d.usecase.On("ArchiveRepo", mock.Anything, mock.Anything).Return(&http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					Header:     make(http.Header),
					Body: io.NopCloser(
						bytes.NewReader([]byte(`{"message": "hello world"}`)),
					),
					ContentLength: int64(len([]byte(`{"message": "hello world"}`))),
				}, nil)
			},
			paramInput: dto.ArchiveRepositoryInput{
				RepoID: repoID,
				Type:   "tar",
				Ref:    "main",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest(
				"GET",
				fmt.Sprintf(
					"/repositories/%s/archive?ref=%s&type=%s",
					repoID.String(),
					tt.paramInput.Ref,
					tt.paramInput.Type,
				),
				nil,
			)

			h.ArchiveRepository(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestGetRepositoryMember(t *testing.T) {
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	repoType := enums.RepoType_Spaces
	namespace := "test-namespace"
	repoName := "test-repo"
	repoID := types.NewRepoID(repoType, namespace, repoName)
	memberID := uuid.New()
	currentUserID := uuid.New()

	tests := []struct {
		name          string
		ctxValue      uuid.UUID
		expStatusCode int
		mockFn        func(d *dependencies)
		expectErr     error
		expOutput     *entities.RepoMember
		invalidRepoID bool
	}{
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:     "should return error if repository not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name:     "should return error if user not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrUserNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrUserNotFound,
		},
		{
			name:     "should return error if member not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrMemberNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrMemberNotFound,
		},
		{
			name:     "should return internal error if GetMember return error",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(nil, errors.New("Error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name:     "should return empty members successfully",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetMember", mock.Anything, mock.Anything).
					Return(&entities.RepoMember{}, nil)
			},
			expStatusCode: http.StatusOK,
			expOutput:     &entities.RepoMember{},
		},
		{
			name:     "should get member successfully",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				member := &entities.RepoMember{
					BaseModel: entities.BaseModel{
						ID: memberID,
					},
					UserID: memberID,
					Role:   enums.RepoRole_Developer,
				}
				d.usecase.On("GetMember", mock.Anything, mock.Anything).Return(member, nil)
			},
			expStatusCode: http.StatusOK,
			expOutput: &entities.RepoMember{
				BaseModel: entities.BaseModel{
					ID: memberID,
				},
				UserID: memberID,
				Role:   enums.RepoRole_Developer,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			}
			c.Request = httptest.NewRequest(
				"GET",
				fmt.Sprintf("/repositories/%s/members/%s", repoID.String(), memberID.String()),
				nil,
			)

			h.GetRepositoryMember(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response entities.RepoMember
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				if tt.expOutput != nil {
					assert.Equal(t, tt.expOutput.ID, response.ID)
					assert.Equal(t, tt.expOutput.UserID, response.UserID)
					assert.Equal(t, tt.expOutput.Role, response.Role)
				}
			}
		})
	}
}

func TestRemoveRepositoryMember(t *testing.T) {
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	repoType := enums.RepoType_Spaces
	namespace := "test-namespace"
	repoName := "test-repo"
	repoID := types.NewRepoID(repoType, namespace, repoName)
	memberID := uuid.New()
	currentUserID := uuid.New()

	tests := []struct {
		name          string
		ctxValue      uuid.UUID
		expStatusCode int
		mockFn        func(d *dependencies)
		expectErr     error
		invalidRepoID bool
	}{
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error if current user not found",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Failed to get current user id"),
		},
		{
			name:          "should return error if trying to remove yourself",
			ctxValue:      memberID,
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Cannot remove yourself"),
		},
		{
			name:     "should return error if repository not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).
					Return(usecase.ErrRepositoryNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name:     "should return error if member not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).
					Return(usecase.ErrUserNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrUserNotFound,
		},
		{
			name:     "should return error if no permission",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).
					Return(usecase.ErrNoPermission)
			},
			expStatusCode: http.StatusForbidden,
			expectErr:     usecase.ErrNoPermission,
		},
		{
			name:     "should remove member successfully",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("RemoveMember", mock.Anything, mock.Anything).Return(nil)
			},
			expStatusCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			}
			c.Request = httptest.NewRequest(
				"DELETE",
				fmt.Sprintf("/repositories/%s/members/%s", repoID.String(), memberID.String()),
				nil,
			)

			h.RemoveRepositoryMember(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, "Remove member successfully", response["message"])
			}
		})
	}
}

func TestUpdateRepositoryMember(t *testing.T) {
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	repoType := enums.RepoType_Spaces
	namespace := "test-namespace"
	repoName := "test-repo"
	repoID := types.NewRepoID(repoType, namespace, repoName)
	memberID := uuid.New()
	currentUserID := uuid.New()
	expireTime := time.Now().Add(24 * time.Hour)

	tests := []struct {
		name          string
		ctxValue      uuid.UUID
		expStatusCode int
		mockFn        func(d *dependencies)
		expectErr     error
		input         dto.UpdateMemberRepositoryInput
		invalidRepoID bool
	}{
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:          "should return error if current user not found",
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Failed to get current user id"),
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
		},
		{
			name:          "should return error if trying to update yourself",
			ctxValue:      memberID,
			expStatusCode: http.StatusForbidden,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Cannot update yourself"),
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
		},
		{
			name:     "should return error if repository not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).
					Return(usecase.ErrRepositoryNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
		},
		{
			name:     "should return error if member not found",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).
					Return(usecase.ErrUserNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrUserNotFound,
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
		},
		{
			name:     "should return error if no permission",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).
					Return(usecase.ErrNoPermission)
			},
			expStatusCode: http.StatusForbidden,
			expectErr:     usecase.ErrNoPermission,
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
		},
		{
			name:     "should update member successfully",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateMember", mock.Anything, mock.Anything).Return(nil)
			},
			input: dto.UpdateMemberRepositoryInput{
				Role:     enums.RepoRole_Developer,
				ExpireAt: &expireTime,
			},
			expStatusCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
					{Key: "member_id", Value: memberID.String()},
				}
			}

			// Marshal input to JSON
			jsonInput, err := json.Marshal(tt.input)
			assert.NoError(t, err)

			c.Request = httptest.NewRequest(
				"PUT",
				fmt.Sprintf("/repositories/%s/members/%s", repoID.String(), memberID.String()),
				bytes.NewBuffer(jsonInput),
			)
			c.Request.Header.Set("Content-Type", "application/json")

			h.UpdateRepositoryMember(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestListRepoTemplates(t *testing.T) {
	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		expectErr     error
		ctxValue      uuid.UUID
		expStatusCode int
		expOutput     []dto.RepoTemplate
	}{
		{
			name:          "should return error if list templates fail",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusInternalServerError,
			mockFn: func(d *dependencies) {
				d.usecase.On("ListRepoTemplates", mock.Anything).Return(nil, errors.New("Error"))
			},
			expectErr: errors.New("Error"),
		},
		{
			name:          "should list repository templates successfully",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusOK,
			mockFn: func(d *dependencies) {
				templates := []dto.RepoTemplate{
					{
						ID:        uuid.New(),
						Type:      "model",
						Name:      "template1",
						Icon:      "icon1",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
					{
						ID:        uuid.New(),
						Type:      "dataset",
						Name:      "template2",
						Icon:      "icon2",
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
				}
				d.usecase.On("ListRepoTemplates", mock.Anything).Return(templates, nil)
			},
			expOutput: []dto.RepoTemplate{
				{
					ID:        uuid.New(),
					Type:      "model",
					Name:      "template1",
					Icon:      "icon1",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
				{
					ID:        uuid.New(),
					Type:      "dataset",
					Name:      "template2",
					Icon:      "icon2",
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			c.Request = httptest.NewRequest("GET", "/repositories/templates", nil)

			h.ListRepoTemplates(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response []dto.RepoTemplate
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				assert.Equal(t, len(tt.expOutput), len(response))
				for i, template := range response {
					assert.Equal(t, tt.expOutput[i].Type, template.Type)
					assert.Equal(t, tt.expOutput[i].Name, template.Name)
					assert.Equal(t, tt.expOutput[i].Icon, template.Icon)
				}
			}
		})
	}
}

func TestGetEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
		config  *configs.GlobalConfig
	}

	repoUUID := uuid.New()
	currentUserID := uuid.New()
	repoType := enums.RepoType_Spaces
	namespace := "test-namespace"
	repoName := "test-repo"
	envVars := &dto.GetRepositoryEnvResponse{
		Data: &dto.RepoEnv{
			RepoID: repoUUID,
			Envs: []dto.EnvKV{
				{
					Key:   "TEST_KEY",
					Value: "test_value",
				},
			},
		},
	}

	tests := []struct {
		name     string
		mockFn   func(d *dependencies)
		ctxValue uuid.UUID

		expStatusCode int
		expResponse   *dto.GetRepositoryEnvResponse
		expectErr     error
		invalidRepoID bool
	}{
		{
			name:          "should return error if repository ID is invalid",
			ctxValue:      uuid.New(),
			expStatusCode: http.StatusBadRequest,
			mockFn:        func(d *dependencies) {},
			expectErr:     errors.New("Invalid repository type"),
			invalidRepoID: true,
		},
		{
			name:     "should return envs successfully",
			ctxValue: currentUserID,
			mockFn: func(d *dependencies) {
				d.usecase.On("GetEnv", mock.Anything, mock.Anything).Return(envVars, nil)
			},
			expStatusCode: http.StatusOK,
			expResponse:   envVars,
		},
		{
			name: "should return usecase.ErrNotSpaceRepo",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetEnv", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrNotSpaceRepo)
			},
			expStatusCode: http.StatusBadRequest,
			expectErr:     usecase.ErrNotSpaceRepo,
		},
		{
			name: "repository not found",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetEnv", mock.Anything, mock.Anything).
					Return(nil, usecase.ErrRepositoryNotFound)
			},
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name: "internal error",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetEnv", mock.Anything, mock.Anything).
					Return(nil, errors.New("internal error"))
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal error"),
		},
		{
			name: "shoudl return empty envs successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("GetEnv", mock.Anything, mock.Anything).
					Return(&dto.GetRepositoryEnvResponse{
						Data: &dto.RepoEnv{
							RepoID: repoUUID,
							Envs:   []dto.EnvKV{},
						},
					}, nil)
			},
			expStatusCode: http.StatusOK,
			expResponse: &dto.GetRepositoryEnvResponse{
				Data: &dto.RepoEnv{
					RepoID: repoUUID,
					Envs:   []dto.EnvKV{},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: mocks.NewMockRepoUsecase(t),
				config:  &configs.GlobalConfig{},
			}
			tt.mockFn(d)

			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			c.Set("user_id", tt.ctxValue)
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: "invalid-type"},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			} else {
				c.Params = gin.Params{
					{Key: enums.REPO_TYPE, Value: repoType.String()},
					{Key: enums.NAMESPACE, Value: namespace},
					{Key: enums.REPO_NAME, Value: repoName},
				}
			}
			c.Request = httptest.NewRequest("GET", "/repositories/envs", nil)

			h.GetEnv(c)

			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response dto.GetRepositoryEnvResponse
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
			}
		})
	}
}

func TestCreateEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
		config  *configs.GlobalConfig
	}

	envRequest := dto.CreateRepositoryEnvRequest{
		Key:   "TEST_KEY",
		Value: new(string),
	}
	*envRequest.Value = "test_value"

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		repoID        *types.RepoID
		requestBody   dto.CreateRepositoryEnvRequest
		expStatusCode int
		expectErr     error
		invalidRepoID bool
	}{
		{
			name: "success",
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusNoContent,
		},
		{
			name: "not a space repo",
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrNotSpaceRepo)
			},
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusBadRequest,
			expectErr:     usecase.ErrNotSpaceRepo,
		},
		{
			name: "repository not found",
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrRepositoryNotFound)
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name: "internal error",
			mockFn: func(d *dependencies) {
				d.usecase.On("CreateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(errors.New("Internal error"))
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal error"),
		},
		{
			name:   "invalid request body",
			mockFn: func(d *dependencies) {},
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody: dto.CreateRepositoryEnvRequest{
				Key:   "Invalid key",
				Value: utils.Ptr("value"),
			},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Key may contain letters, numbers or underscores"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
				config:  &configs.GlobalConfig{},
			}
			test.mockFn(d)

			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set up request
			reqBody, _ := json.Marshal(test.requestBody)
			c.Request = httptest.NewRequest(
				http.MethodPost,
				"/",
				strings.NewReader(string(reqBody)),
			)
			c.Request.Header.Set("Content-Type", "application/json")

			// Set up path parameters
			if test.invalidRepoID {
				c.Params = gin.Params{
					{Key: "repo_type", Value: "invalid-type"},
					{Key: "namespace", Value: *test.repoID.Namespace()},
					{Key: "repo_name", Value: *test.repoID.RepoName()},
				}
			} else {
				c.Params = gin.Params{
					{Key: "repo_type", Value: test.repoID.RepoType().String()},
					{Key: "namespace", Value: *test.repoID.Namespace()},
					{Key: "repo_name", Value: *test.repoID.RepoName()},
				}
			}

			// Create handler and call function
			handler := handlers.NewRepositoryHandler(nil, d.usecase, nil)
			handler.CreateEnv(c)

			// Assertions
			assert.Equal(t, test.expStatusCode, w.Code)
			if test.expectErr == nil {
				assert.Empty(t, w.Body.String())
			} else {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, test.expectErr.Error(), response.Message)
				assert.Equal(t, test.expStatusCode, w.Code)
			}
		})
	}
}

func TestUpdateEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
		config  *configs.GlobalConfig
	}

	envRequest := dto.UpdateRepositoryEnvRequest{
		OldKey: "OLD_KEY",
		Key:    "NEW_KEY",
		Value:  new(string),
	}
	*envRequest.Value = "new_value"

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		repoID        *types.RepoID
		requestBody   dto.UpdateRepositoryEnvRequest
		expStatusCode int
		expectErr     error
		invalidRepoID bool
	}{
		{
			name: "should update environment variable successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusNoContent,
		},
		{
			name: "should return error if not a space repo",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrNotSpaceRepo)
			},
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusBadRequest,
			expectErr:     usecase.ErrNotSpaceRepo,
		},
		{
			name: "should return error if repository not found",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrRepositoryNotFound)
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name: "should return error if internal error occurs",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(errors.New("Internal error"))
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody:   envRequest,
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal error"),
		},
		{
			name:   "should return error if OldKey in request body is invalid",
			mockFn: func(d *dependencies) {},
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody: dto.UpdateRepositoryEnvRequest{
				OldKey: "invalid old key",
				Key:    "valid_key",
				Value:  utils.Ptr("value"),
			},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("OldKey may contain letters, numbers or underscores"),
		},
		{
			name:   "should return error if OldKey in request body is invalid",
			mockFn: func(d *dependencies) {},
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestBody: dto.UpdateRepositoryEnvRequest{
				OldKey: "valid_old_key",
				Key:    "invalid key",
				Value:  utils.Ptr("value"),
			},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Key may contain letters, numbers or underscores"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: mocks.NewMockRepoUsecase(t),
				config:  &configs.GlobalConfig{},
			}
			tt.mockFn(d)

			handler := handlers.NewRepositoryHandler(d.config, d.usecase, nil)

			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set up request body
			reqBody, _ := json.Marshal(tt.requestBody)
			c.Request = httptest.NewRequest(http.MethodPut, "/", bytes.NewBuffer(reqBody))
			c.Request.Header.Set("Content-Type", "application/json")
			if tt.invalidRepoID {
				c.Params = gin.Params{
					{Key: "repo_type", Value: "invalid-type"},
					{Key: "namespace", Value: *tt.repoID.Namespace()},
					{Key: "repo_name", Value: *tt.repoID.RepoName()},
				}
			} else {
				c.Params = gin.Params{
					{Key: "repo_type", Value: tt.repoID.RepoType().String()},
					{Key: "namespace", Value: *tt.repoID.Namespace()},
					{Key: "repo_name", Value: *tt.repoID.RepoName()},
				}
			}

			handler.UpdateEnv(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
			}
		})
	}
}

func TestDeleteEnv(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
		config  *configs.GlobalConfig
	}

	envRequest := dto.DeleteRepositoryEnvRequest{
		Key: "TEST_KEY",
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		repoID        *types.RepoID
		requestQuery  dto.DeleteRepositoryEnvRequest
		expStatusCode int
		expectErr     error
		invalidRepoID bool
	}{
		{
			name: "success",
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteEnv", mock.Anything, mock.Anything, mock.Anything).Return(nil)
			},
			repoID:        types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestQuery:  envRequest,
			expStatusCode: http.StatusNoContent,
		},
		{
			name:   "invalid request query",
			mockFn: func(d *dependencies) {},
			repoID: types.NewRepoID(enums.RepoType_Spaces, "namespace", "repoName"),
			requestQuery: dto.DeleteRepositoryEnvRequest{
				Key: "invalid key",
			},
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Key may contain letters, numbers or underscores"),
		},
		{
			name: "not a space repo",
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrNotSpaceRepo)
			},
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repoName"),
			requestQuery:  envRequest,
			expStatusCode: http.StatusBadRequest,
			expectErr:     errors.New("Repository type is not space repository"),
		},
		{
			name: "repository not found",
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(usecase.ErrRepositoryNotFound)
			},
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repoName"),
			requestQuery:  envRequest,
			expStatusCode: http.StatusNotFound,
			expectErr:     usecase.ErrRepositoryNotFound,
		},
		{
			name: "internal error",
			mockFn: func(d *dependencies) {
				d.usecase.On("DeleteEnv", mock.Anything, mock.Anything, mock.Anything).
					Return(errors.New("Internal error"))
			},
			repoID:        types.NewRepoID(enums.RepoType_Models, "namespace", "repoName"),
			requestQuery:  envRequest,
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Internal error"),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Setup
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
				config:  &configs.GlobalConfig{},
			}
			test.mockFn(d)

			handler := handlers.NewRepositoryHandler(d.config, d.usecase, nil)

			// Create test context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)

			// Set up request
			c.Request = httptest.NewRequest(
				http.MethodDelete,
				"/repositories/spaces/namespace/repo-name/envs",
				nil,
			)
			c.Request.URL.RawQuery = fmt.Sprintf("key=%s", test.requestQuery.Key)

			// Set up path parameters
			if test.invalidRepoID {
				c.Params = gin.Params{
					{Key: "repo_type", Value: "invalid-type"},
					{Key: "namespace", Value: "namespace"},
					{Key: "repo_name", Value: "repo-name"},
				}
			} else {
				c.Params = gin.Params{
					{Key: "repo_type", Value: "spaces"},
					{Key: "namespace", Value: "namespace"},
					{Key: "repo_name", Value: "repo-name"},
				}
			}

			// Execute
			handler.DeleteEnv(c)

			// Assert
			assert.Equal(t, test.expStatusCode, w.Code)
			if test.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, test.expectErr.Error(), response.Message)
			}
		})
	}
}

func TestPushEventWebhookHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type dependencies struct {
		usecase *mocks.MockRepoUsecase
	}

	tests := []struct {
		name          string
		mockFn        func(d *dependencies)
		payload       dto.GitLabPushEvent
		expStatusCode int
		expectErr     error
	}{
		{
			name:   "should return error if payload is invalid",
			mockFn: func(d *dependencies) {},
			payload: dto.GitLabPushEvent{
				Ref: "refs/heads/feature",
			},
			expStatusCode: http.StatusOK,
		},
		{
			name: "should return error if update tags fail",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateTagsInRepository", mock.Anything, mock.Anything).
					Return(errors.New("Error"))
			},
			payload: dto.GitLabPushEvent{
				Ref: "refs/heads/main",
			},
			expStatusCode: http.StatusInternalServerError,
			expectErr:     errors.New("Error"),
		},
		{
			name: "should handle webhook successfully",
			mockFn: func(d *dependencies) {
				d.usecase.On("UpdateTagsInRepository", mock.Anything, mock.Anything).
					Return(nil)
			},
			payload: dto.GitLabPushEvent{
				Ref: "refs/heads/main",
			},
			expStatusCode: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &dependencies{
				usecase: &mocks.MockRepoUsecase{},
			}
			tt.mockFn(d)
			h := handlers.NewRepositoryHandler(nil, d.usecase, nil)

			// Setup Gin context
			w := httptest.NewRecorder()
			c, _ := gin.CreateTestContext(w)
			jsonInput, _ := json.Marshal(tt.payload)
			c.Request = httptest.NewRequest(
				"POST",
				"/gitlab/webhook/push_event",
				strings.NewReader(string(jsonInput)),
			)

			h.PushEventWebhookHandler(c)

			assert.Equal(t, tt.expStatusCode, w.Code)
			if tt.expectErr != nil {
				var response dto.HTTPError
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectErr.Error(), response.Message)
				assert.Equal(t, tt.expStatusCode, w.Code)
			} else {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)
				assert.Equal(t, tt.expStatusCode, w.Code)
				if tt.payload.Ref == "refs/heads/main" {
					assert.Equal(t, "Callback handle successfully", response["message"])
				} else {
					assert.Equal(t, "Ignored push event. Not on main branch.", response["message"])
				}
			}
		})
	}
}
