package dto

import (
	"mime/multipart"
	"time"

	"github.com/google/uuid"

	"api-server/internal/entities"
	"api-server/internal/enums"
)

type CreateOrganizationInput struct {
	Name             string                 `json:"name" form:"name" binding:"required" validate:"required,name,gitlabReservedGroups"`
	PathName         string                 `json:"path_name" form:"path_name" binding:"required" validate:"required,username,gitlabReservedGroups"`
	OrganizationType enums.OrganizationType `json:"organization_type" form:"organization_type" binding:"required" validate:"required,oneof=company university project open_source"`
	File             *multipart.FileHeader  `form:"file"`
	Interest         string                 `json:"interest" form:"interest" validate:"omitempty,max=1000"`
	UserCurrentId    uuid.UUID
} // @name CreateOrganizationInput

type CreateOrganizationOutput struct {
	ID uuid.UUID `json:"uuid"`
} // @name CreateOrganizationOutput

type DeleteOrganizationInput struct {
	Id            uuid.UUID `json:"id"              swaggerignore:"true"`
	CurrentUserId uuid.UUID `json:"current_user_id" swaggerignore:"true"`
}

type UpdateOrganizationInput struct {
	Interest      *string `json:"interest"                             validate:"omitempty,max=1000"`
	UserCurrentId uuid.UUID
	OrgId         uuid.UUID
} // @name UpdateOrganizationInput

type Organization struct {
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	PathName         string                 `json:"path_name"`
	Interest         string                 `json:"interest"`
	OrganizationType enums.OrganizationType `json:"organization_type"`
} // @name Organization

func (o Organization) FromEntity(e entities.Organization) Organization {
	o.ID = e.ID.String()
	o.Name = e.Name
	o.PathName = e.PathName
	o.OrganizationType = e.Type

	if e.Interest != nil {
		o.Interest = *e.Interest
	}

	return o
}

type InviteOrgMemberInput struct {
	UserId        uuid.UUID     `json:"user_id"   validate:"required"`
	OrgId         uuid.UUID     `json:"org_id"    validate:"required"                       swaggerignore:"true"`
	Role          enums.OrgRole `json:"role"      validate:"required,oneof=owner developer"`
	ExpireAt      *time.Time    `json:"expire_at"`
	CurrentUserID uuid.UUID     `json:"-"`
}

type InviteOrgMembersInput struct {
	OrgId         uuid.UUID              `json:"org_id"  validate:"required"                   swaggerignore:"true"`
	Members       []OrgMemberInviteInput `json:"members" validate:"required,min=1,max=15,dive"`
	CurrentUserID uuid.UUID              `json:"-"`
}

// RepoMemberInviteInput defines the input for inviting a single member within the bulk invite
type OrgMemberInviteInput struct {
	UserId   uuid.UUID     `json:"user_id"   validate:"required"`
	Role     enums.OrgRole `json:"role"      validate:"required,oneof=owner developer"`
	ExpireAt *time.Time    `json:"expire_at"`
}

type ListOrganizationsInput struct {
	Paginate PaginateRequest
	UserId   uuid.UUID      `json:"user_id"`
	Keyword  string         `               form:"keyword" validate:"omitempty,keyword"`
	Role     enums.RepoRole `               form:"role"    validate:"omitempty,oneof=owner developer"`
}

type FindOrganizationInput struct {
	OrgId         uuid.UUID `json:"org_id"  uri:"org_id" binding:"required"`
	CurrentUserID uuid.UUID `json:"user_id"`
} // @name FindOrganizationInput

type FindOrganizationOutput struct {
	entities.Organization
} // @name FindOrganizationOutput

type OrgReposInfo struct {
	Org          entities.Organization `json:"org"`
	Repositories []entities.Repository `json:"repos"`
}

type ListOrganizationsOutput HTTPResponse[[]OrgReposInfo]

// @name ListOrganizationsOutput

type ListOrgMembersInput struct {
	Paginate      PaginateRequest
	Keyword       string    `form:"keyword" validate:"omitempty,keyword"`
	OrgId         uuid.UUID `                                            json:"org_id"`
	CurrentUserID uuid.UUID `                                            json:"user_id"`
} // @name ListOrgMembersInput

type ListOrgMembersOutput HTTPResponse[[]entities.OrgMemberInfo]

// @name ListOrgMembersOutput

type GetMemberOrganizaionInput struct {
	OrgId    uuid.UUID `json:"org_id"    uri:"org_id" binding:"required"`
	MemberId uuid.UUID `json:"member_id" uri:"org_id" binding:"required"`
} // @name RemoveMemberOrganizaionInput

type RemoveMemberOrganizaionInput struct {
	OrgId         uuid.UUID `json:"org_id"    uri:"org_id" binding:"required"`
	MemberId      uuid.UUID `json:"member_id" uri:"org_id" binding:"required"`
	CurrentUserID uuid.UUID `json:"user_id"`
} // @name RemoveMemberOrganizaionInput

type UpdateMemberOrganizaionInput struct {
	OrgId         uuid.UUID     `json:"org_id"    uri:"org_id" swaggerignore:"true"`
	MemberId      uuid.UUID     `json:"member_id" uri:"org_id" swaggerignore:"true"`
	Role          enums.OrgRole `json:"role"                                        validate:"required,oneof=owner developer"`
	ExpireAt      *time.Time    `json:"expire_at"`
	CurrentUserID uuid.UUID     `json:"user_id"                swaggerignore:"true"`
} // @name UpdateMemberOrganizaionInput

type UploadOrganizationAvatarInput struct {
	OrgID         uuid.UUID             `json:"org_id"          required:"true" swaggerignore:"true"`
	CurrentUserId uuid.UUID             `json:"current_user_id"                 swaggerignore:"true"`
	File          *multipart.FileHeader `json:"file"            required:"true"                      binding:"required"`
} // @name UploadOrganizationAvatarInput

type DeleteOrganizationAvatarInput struct {
	OrgID         uuid.UUID `json:"org_id"          required:"true" swaggerignore:"true"`
	CurrentUserId uuid.UUID `json:"current_user_id"                 swaggerignore:"true"`
}
