-- create new type custom_image_deployment_type
CREATE TYPE "custom_image_deployment_type" AS ENUM (
  'single',
  'compose'
);

-- update custom_image_deployments to add new columns
ALTER TABLE custom_image_deployments
ADD COLUMN IF NOT EXISTS volumes TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN IF NOT EXISTS author TEXT,
ADD COLUMN IF NOT EXISTS compose_ports TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN IF NOT EXISTS restart_policy TEXT,
ADD COLUMN IF NOT EXISTS deployment_type custom_image_deployment_type DEFAULT 'single',
ADD COLUMN IF NOT EXISTS repo_id UUID,
ADD COLUMN IF NOT EXISTS namespace TEXT,
ADD COLUMN IF NOT EXISTS error_message TEXT;

-- remove unique constraint on deployment_name
ALTER TABLE custom_image_deployments DROP CONSTRAINT IF EXISTS custom_image_deployments_deployment_name_key;

-- add unique constraint on deployment_name, namespace
ALTER TABLE custom_image_deployments
ADD CONSTRAINT custom_image_deployments_deployment_name_namespace_key UNIQUE (deployment_name, namespace);

-- add foreign key constraint on repo_id, when repositories deleted, delete deployments
ALTER TABLE custom_image_deployments ADD FOREIGN KEY ("repo_id") REFERENCES "repositories" ("id") ON DELETE CASCADE;

-- update current deployments to set deployment_type to single
UPDATE custom_image_deployments
SET deployment_type = 'single'
WHERE deployment_type IS NULL;

-- update current deployments to set namespace to custom-image
UPDATE custom_image_deployments
SET namespace = 'custom-image'
WHERE namespace IS NULL;
