services:
  # GPU-enabled service with general GPU requirements
  ml-training:
    image: tensorflow/tensorflow:latest-gpu
    ports:
      - "8888:8888"
    labels:
      # Enable GPU with general tolerations - will run on any GPU node
      volvo.gpu: true
      # Service exposure
      volvo.service.expose: true
      # Resource requirements
      volvo.deployment.request.memory: "2Gi"
      volvo.deployment.request.cpu: "1000m"
      volvo.deployment.limit.memory: "4Gi"
      volvo.deployment.limit.cpu: "2000m"
      # Application labels
      app: ml-training
      version: "1.0"

  # GPU-enabled service with specific GPU type requirements
  inference-service:
    image: nvidia/cuda:11.8-runtime-ubuntu20.04
    ports:
      - "5000:5000"
    labels:
      # Enable GPU with specific Tesla-T4 requirements
      volvo.gpu: Tesla-T4
      # Service exposure
      volvo.service.expose: true
      # Resource requirements
      volvo.deployment.request.memory: "1Gi"
      volvo.deployment.request.cpu: "500m"
      volvo.deployment.limit.memory: "2Gi"
      volvo.deployment.limit.cpu: "1000m"
      # Application labels
      app: inference-service
      version: "2.0"

  # CPU-only service (no GPU)
  web-frontend:
    image: nginx:latest
    ports:
      - "80:80"
    labels:
      # Explicitly disable GPU (optional, default is no GPU)
      volvo.gpu: false
      # Service exposure
      volvo.service.expose: true
      # Resource requirements
      volvo.deployment.request.memory: "128Mi"
      volvo.deployment.request.cpu: "100m"
      volvo.deployment.limit.memory: "256Mi"
      volvo.deployment.limit.cpu: "200m"
      # Application labels
      app: web-frontend
      version: "1.0"

  # Another GPU service with different GPU type
  data-processing:
    image: pytorch/pytorch:latest
    ports:
      - "8080:8080"
    labels:
      # Enable GPU with specific A100 requirements
      volvo.gpu: A100-SXM4-40GB
      # Service exposure
      volvo.service.expose: true
      # Higher resource requirements for data processing
      volvo.deployment.request.memory: "8Gi"
      volvo.deployment.request.cpu: "2000m"
      volvo.deployment.limit.memory: "16Gi"
      volvo.deployment.limit.cpu: "4000m"
      # Larger proxy body size for data uploads
      volvo.ingress.proxy-body-size: "100m"
      # Application labels
      app: data-processing
      version: "3.0"

# This docker-compose file demonstrates the new volvo.gpu label:
#
# 1. volvo.gpu: true
#    - Enables GPU support with general tolerations
#    - Adds nvidia.com/gpu toleration with NoSchedule effect
#    - Sets node selector to nvidia.com/gpu: "true"
#    - Adds GPU resource request and limit (1 GPU)
#    - Suitable for deployments that can run on any GPU node
#
# 2. volvo.gpu: Tesla-T4 (or any specific GPU type)
#    - Enables GPU support with specific GPU type requirements
#    - Adds nvidia.com/gpu toleration with NoSchedule effect
#    - Sets node selector to nvidia.com/gpu.product: "Tesla-T4"
#    - Adds GPU resource request and limit (1 GPU)
#    - Ensures deployment runs only on nodes with the specified GPU type
#
# 3. volvo.gpu: false
#    - Explicitly disables GPU support (optional, default behavior)
#    - No GPU tolerations or node selectors added
#    - No GPU resource requests or limits
#
# Valid GPU type formats:
# - Tesla-T4, Tesla-V100, Tesla-K80
# - A100-SXM4-40GB, A100-PCIE-40GB
# - RTX-3090, RTX-4090
# - Any alphanumeric string with hyphens
#
# AWS EKS GPU Node Labels:
# - AWS automatically adds nvidia.com/gpu.product label to GPU nodes
# - Common values: Tesla-T4, Tesla-V100, A100-SXM4-40GB
# - The label value matches the actual GPU model in the node
#
# Kubernetes Resources Added:
# - GPU requests: nvidia.com/gpu: "1"
# - GPU limits: nvidia.com/gpu: "1"
# - Tolerations: key=nvidia.com/gpu, operator=Exists, effect=NoSchedule
# - Node selectors: nvidia.com/gpu=true OR nvidia.com/gpu.product=<gpu-type>
#
# These labels will be:
# 1. Validated for correct format during transformation
# 2. Removed from the docker-compose content
# 3. Applied as GPU requirements, tolerations, and node selectors
# 4. Added as GPU resource requests/limits to the container resources
