services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      # Service exposure
      volvo.service.expose: true
      # Resource requests (minimum guaranteed resources)
      volvo.deployment.request.memory: "256Mi"
      volvo.deployment.request.cpu: "100m"
      # Resource limits (maximum allowed resources)
      volvo.deployment.limit.memory: "512Mi"
      volvo.deployment.limit.cpu: "200m"
      # Ingress configuration
      volvo.ingress.proxy-body-size: "10m"
      # Application labels
      app: web-frontend
      version: "1.0"

  api:
    image: node:18-alpine
    ports:
      - "3000:3000"
    labels:
      # Service exposure
      volvo.service.expose: true
      # Higher resource requirements for API service
      volvo.deployment.request.memory: "512Mi"
      volvo.deployment.request.cpu: "250m"
      volvo.deployment.limit.memory: "1Gi"
      volvo.deployment.limit.cpu: "500m"
      # Larger proxy body size for API uploads
      volvo.ingress.proxy-body-size: "50m"
      # Application labels
      app: api-backend
      version: "2.1"

  database:
    image: postgres:15
    environment:
      POSTGRES_DB: myapp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - db_data:/var/lib/postgresql/data
    labels:
      # Database doesn't need ingress exposure
      # Database resource requirements
      volvo.deployment.request.memory: "1Gi"
      volvo.deployment.request.cpu: "500m"
      volvo.deployment.limit.memory: "2Gi"
      volvo.deployment.limit.cpu: "1000m"
      # Volume configuration
      volvo.volume.size: "10Gi"
      # Application labels
      app: database
      version: "15"

  worker:
    image: python:3.11-slim
    labels:
      # Background worker - minimal resources
      volvo.deployment.request.memory: "128Mi"
      volvo.deployment.request.cpu: "50m"
      volvo.deployment.limit.memory: "256Mi"
      volvo.deployment.limit.cpu: "100m"
      # Application labels
      app: background-worker
      version: "1.0"

volumes:
  db_data:

# This docker-compose file demonstrates the new volvo.deployment.* labels:
#
# 1. volvo.deployment.request.memory - Sets memory request (guaranteed minimum)
# 2. volvo.deployment.request.cpu - Sets CPU request (guaranteed minimum)
# 3. volvo.deployment.limit.memory - Sets memory limit (maximum allowed)
# 4. volvo.deployment.limit.cpu - Sets CPU limit (maximum allowed)
#
# Valid memory formats: 128Mi, 1Gi, 512Ki, 2G, 1024M, etc.
# Valid CPU formats: 100m (millicores), 0.1, 1, 2.5, etc.
#
# These labels will be:
# 1. Validated for correct format during transformation
# 2. Removed from the docker-compose content
# 3. Applied directly to the Kubernetes Deployment manifests
# 4. Set as resource requests/limits on the main container
#
# The transformation process:
# 1. Parse docker-compose content
# 2. Extract and validate volvo.deployment.* labels
# 3. Remove these labels from the compose content
# 4. Store resource requirements separately
# 5. Apply resource requirements to Kubernetes Deployment manifests
# 6. Deploy to cluster with proper resource constraints
