---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.service.type: ClusterIP
    kompose.version: 1.36.0 (HEAD)
    kompose.volume.size: 1Gi
    kompose.volume.storage-class-name: volvo-efs-sc
  labels:
    io.kompose.service: postgres-a
  name: postgres-a
  namespace: ns1
spec:
  ports:
    - name: "5432"
      port: 5432
      targetPort: 5432
    - name: "5431"
      port: 5431
      targetPort: 5432
  selector:
    io.kompose.service: postgres-a
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.version: 1.36.0 (HEAD)
    kompose.volume.size: 10Gi
    kompose.volume.storage-class-name: volvo-efs-sc
  labels:
    io.kompose.service: redisa-b-c
  name: redisa-b_c
  namespace: ns1
spec:
  ports:
    - name: "6379"
      port: 6379
      targetPort: 6379
    - name: "6666"
      port: 6666
      targetPort: 6379
  selector:
    io.kompose.service: redisa-b-c

---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.service.expose: "true"
    kompose.service.expose.ingress-class-name: nginx
    kompose.version: 1.36.0 (HEAD)
  labels:
    io.kompose.service: weba-c-b
  name: weba-c_b
  namespace: ns1
spec:
  ports:
    - name: "8000"
      port: 8000
      targetPort: 80
  selector:
    io.kompose.service: weba-c-b

---
apiVersion: v1
kind: Namespace
metadata:
  name: ns1
  namespace: ns1

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.service.type: ClusterIP
    kompose.version: 1.36.0 (HEAD)
    kompose.volume.size: 1Gi
    kompose.volume.storage-class-name: volvo-efs-sc
  labels:
    io.kompose.service: postgres-a
  name: postgres-a
  namespace: ns1
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: postgres-a
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: /tmp/go-build1928680257/b001/exe/main
        kompose.service.type: ClusterIP
        kompose.version: 1.36.0 (HEAD)
        kompose.volume.size: 1Gi
        kompose.volume.storage-class-name: volvo-efs-sc
      labels:
        io.kompose.service: postgres-a
    spec:
      containers:
        - env:
            - name: POSTGRES_DB
              value: mydb
            - name: POSTGRES_PASSWORD
              value: mypassword
            - name: POSTGRES_USER
              value: myuser
          image: postgres:16
          name: postgres-a
          ports:
            - containerPort: 5432
              protocol: TCP
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: pgdata
            - mountPath: /bbb
              name: aaa
      restartPolicy: Always
      volumes:
        - name: pgdata
          persistentVolumeClaim:
            claimName: pgdata
        - name: aaa
          persistentVolumeClaim:
            claimName: aaa

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: pgdata
  name: pgdata
  namespace: ns1
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: volvo-efs-sc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: aaa
  name: aaa
  namespace: ns1
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: volvo-efs-sc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.version: 1.36.0 (HEAD)
    kompose.volume.size: 10Gi
    kompose.volume.storage-class-name: volvo-efs-sc
  labels:
    io.kompose.service: redisa-b-c
  name: redisa-b-c
  namespace: ns1
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: redisa-b-c
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: /tmp/go-build1928680257/b001/exe/main
        kompose.version: 1.36.0 (HEAD)
        kompose.volume.size: 10Gi
        kompose.volume.storage-class-name: volvo-efs-sc
      labels:
        io.kompose.service: redisa-b-c
    spec:
      containers:
        - image: redis:alpine
          name: redisa-b-c
          ports:
            - containerPort: 6379
              protocol: TCP
          volumeMounts:
            - mountPath: /data
              name: data
      restartPolicy: Always
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: data

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  labels:
    io.kompose.service: data
  name: data
  namespace: ns1
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: volvo-efs-sc

---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.service.expose: "true"
    kompose.service.expose.ingress-class-name: nginx
    kompose.version: 1.36.0 (HEAD)
  labels:
    io.kompose.service: weba-c-b
  name: weba-c-b
  namespace: ns1
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: weba-c-b
  template:
    metadata:
      annotations:
        kompose.cmd: /tmp/go-build1928680257/b001/exe/main
        kompose.service.expose: "true"
        kompose.service.expose.ingress-class-name: nginx
        kompose.version: 1.36.0 (HEAD)
      labels:
        io.kompose.service: weba-c-b
    spec:
      containers:
        - env:
            - name: ENV
              value: production
          image: nginx:latest
          name: weba-c-b
          ports:
            - containerPort: 80
              protocol: TCP
      restartPolicy: Always

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kompose.cmd: /tmp/go-build1928680257/b001/exe/main
    kompose.service.expose: "true"
    kompose.service.expose.ingress-class-name: nginx
    kompose.version: 1.36.0 (HEAD)
  labels:
    io.kompose.service: weba-c-b
  name: weba-c-b
  namespace: ns1
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - backend:
              service:
                name: weba-c-b
                port:
                  number: 8000
            path: /
            pathType: Prefix

