package main

import (
	"context"

	kompose_usecase "api-server/internal/usecase/kompose"
	"api-server/pkg/kube"
)

func main() {
	content := `
services:
  weba-c_b:
    image: nginx:latest
    ports:
      - "8000:80"
    environment:
      - ENV=production
    labels:
      kompose.service.expose: true
      kompose.service.expose.ingress-class-name: nginx

  postgres-a:
    image: postgres:16
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydb
    ports:
      - "5432:5432"
      - "5431:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
      - aaa:/bbb
    labels:
      kompose.service.type: "ClusterIP"
      kompose.volume.size: "1Gi"
      kompose.volume.storage-class-name: volvo-efs-sc

  redisa-b_c:
    image: redis:alpine
    ports:
      - "6379:6379"
      - "6666:6379"
    volumes:
      - data:/data
    labels:
      kompose.volume.size: 10Gi
      kompose.volume.storage-class-name: volvo-efs-sc

volumes:
  pgdata: {}
  data: {}
  aaa: {}
`

	resp := convert(content)
	_ = resp
	// apply(resp)

	// delete()
}

func convert(content string) []kompose_usecase.KubernetesManifest {
	// Initialize dependencies
	komposeUsecase := kompose_usecase.New(nil, nil, nil, nil)
	response, err := komposeUsecase.ConvertDockerComposeToKubernetes(context.Background(), kompose_usecase.ConvertDockerComposeInput{
		DockerComposeContent: content,
		Namespace:            "ns1",
		// GenerateNetworkPolicies: true,
		// VolumeType:              "persistentVolumeClaim",
		// PVCRequestSize:          "1Gi",
	})
	if err != nil {
		panic(err)
	}

	// Print the response
	for _, manifest := range response.Manifests {
		println(manifest.Kind, manifest.Name)
		println(manifest.Content)
	}
	println(response.Summary.TotalServices)

	for k, i := range response.Summary.GeneratedResources {
		println(k, i)
	}

	for _, warning := range response.Summary.Warnings {
		println(warning)
	}

	return response.Manifests
}

func apply(manifests []kompose_usecase.KubernetesManifest) {
	kubeClient, err := kube.New(false)
	if err != nil {
		panic(err)
	}
	komposeUsecase := kompose_usecase.New(nil, nil, kubeClient, nil)

	response, err := komposeUsecase.ApplyKubernetesManifests(context.Background(), kompose_usecase.ApplyKubernetesManifestsInput{
		Manifests: manifests,
		Namespace: "ns1",
	})
	if err != nil {
		panic(err)
	}

	// Print the response
	for _, appliedResource := range response.AppliedResources {
		println(appliedResource.Kind, appliedResource.Name, appliedResource.Action, appliedResource.Message)
	}
	println(response.Summary.TotalResources)
	println(response.Summary.SuccessfulResources)
	println(response.Summary.FailedResources)
	for _, action := range response.Summary.Actions {
		println(action)
	}
}

func delete() {
	kubeClient, err := kube.New(false)
	if err != nil {
		panic(err)
	}
	komposeUsecase := kompose_usecase.New(nil, nil, kubeClient, nil)

	// Delete all resources managed by kompose-usecase in the namespace
	response, err := komposeUsecase.DeleteKubernetesManifests(context.Background(), kompose_usecase.DeleteKubernetesManifestsInput{
		// Manifests: manifest,
		Namespace: "ns1",
	})
	if err != nil {
		panic(err)
	}

	// Print the response
	for _, deletedResource := range response.DeletedResources {
		println(deletedResource.Kind, deletedResource.Name, deletedResource.Message)
	}
	println(response.Summary.TotalResources)
	println(response.Summary.SuccessfulResources)
	println(response.Summary.FailedResources)
}
